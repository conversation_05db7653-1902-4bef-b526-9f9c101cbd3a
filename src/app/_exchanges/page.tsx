import Header from "@/components/comman/Header";
import React from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import ExchangesTable from "@/components/exchanges/ExchangesTable";
import { Metadata } from "next";
import Footer from "@/components/comman/Footer";

export const metadata: Metadata = {
  title: "MOFSE - Exchanges",
};

const Exchanges = () => {
  return (
    <section>
      <Header />
      <section className="center py-16">
        <Breadcrumb className="mb-2">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/learn">Exchanges</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <h1 className="text-3xl mb-8">
          <span className="font-semibold">Exchanges </span>
        </h1>

        <ExchangesTable />
      </section>
      <Footer />
    </section>
  );
};

export default Exchanges;
