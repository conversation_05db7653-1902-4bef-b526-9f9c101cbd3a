import Header from "@/components/comman/Header";
import Footer from "@/components/comman/Footer";
import React from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "MOFSE - About Us",
};

const Page = () => {
  return (
    <>
      <Header />
      <section className="center py-16">
        <h3 className="text-2xl font-bold text-bg-primary my-4">ABOUT US</h3>
        <p className="text-text-primary text-justify">
          Our platform serves as a trusted information companion, offering a comprehensive suite of tools and insights. We bridge established frameworks with innovative perspectives, combining on chain and off chain insights to focus on bigger picture. Committed to serving others, we prioritize accessibility and inclusivity for all. For <NAME_EMAIL>

          We pledge to contribute 20% of our profits to causes that align with our values.
        </p>
        <h3 className="text-2xl font-bold text-bg-primary my-4">OUR MISSION</h3>
        <p className="text-text-primary text-justify">
          Our mission is to empower others by simplifying the understanding of
          the digital asset industry through comprehensive asset data and
          educational topics. We are dedicated to providing transparent,
          reliable information that deepens market knowledge, making it easier
          to learn about and track digital assets. By delivering market data,
          digital asset insights, and details on upcoming projects, we enhance
          the accessibility of this technology while upholding the integrity and
          neutrality of our content. Our goal is to streamline digital asset
          tracking and learning, ensuring that users—whether casual observers or
          dedicated enthusiasts—have access to accurate, clear, and actionable
          information
        </p>
        <h3 className="text-2xl font-bold text-bg-primary my-4">OUR VISION</h3>
        <p className="text-text-primary text-justify">
          We envision a future where digital assets are universally understood
          and seamlessly integrated into everyday financial ecosystems. Our
          platform aims to lead the way in digital asset tracking, providing an
          innovative, user-centric approach that removes barriers to
          information. With clarity and precision, we empower users to navigate
          the digital economy with confidence and awareness
        </p>
      </section>
      <Footer />
    </>
  );
};

export default Page;
