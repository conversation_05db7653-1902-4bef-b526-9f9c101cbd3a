import Footer from "@/components/comman/Footer";
import Header from "@/components/comman/Header";
import { Metada<PERSON> } from "next";
import Link from "next/link";
import React from "react";

const ADVERTISE_DATA = [
  {
    title: "Reach a targeted crypto Audience",
    icon: "./ad-1.svg",
    description:
      "Tap into a community of blockchain enthusiasts, investors, and traders actively seeking the latest crypto insights.",
  },
  {
    icon: "./ad-2.svg",
    title: "Premium Visibility",
    description:
      "Get featured on one of the most trusted crypto data aggregation platforms with high-traffic exposure.",
  },
  {
    icon: "./ad-3.svg",
    title: "Drive Real Engagement",
    description:
      "Inspire action, boost conversions, and gain credibility among crypto users and investors.",
  },
  {
    icon: "./ad-4.svg",
    title: "Multiple Ad Formats",
    description:
      "Choose from banner ads, sponsored content, featured project listings, and more to maximize impact.",
  },
  // {
  //   icon: <AD5 />,
  //   title: "Targeted Crypto Audience",
  //   description:
  //     "Engage with millions of crypto investors—primarily retail traders aged 25-34—across all devices, from mobile to desktop.",
  // },
  {
    icon: "./ad-6.svg",
    title: "Geo-Targeting for Maximum Impact",
    // description:
    //   "Tap into a community of blockchain enthusiasts, investors, and traders actively seeking the latest crypto insights.",
  },
  {
    icon: "./ad-7.svg",
    title: "Personalized Onboarding:",
    description:
      "Get started smoothly with a one-on-one onboarding call or reach us anytime via Telegram or Email.",
  },
  // {
  //   icon: <AD8 />,
  //   title: "Detailed Performance Reports:",
  //   description:
  //     "Monitor your campaign with comprehensive weekly or monthly reports, helping you optimize your results.",
  // },
];

export const metadata: Metadata = {
  title: "MOFSE - Advertise with Us",
  description: "Engage Your Audience & Amplify Your Reach",
};

const Page = () => {
  return (
    <>
      <Header />
      <section className="center py-16">
        <h1 className="text-4xl font-bold">Advertise with Us</h1>
        <p className="text-2xl text-text-secondary font-medium my-4">
          Engage Your Audience & Amplify Your Reach
        </p>
        <p className="text-text-secondary">
          At MOFSE, we provide a powerful platform for businesses, projects, and
          innovators in the crypto and Web3 space to connect with a highly
          engaged audience. Whether you&apos;re launching a new cryptocurrency,
          promoting a blockchain product, or looking to increase brand
          awareness, our advertising solutions help you reach the right people
          at the right time.
        </p>
        <hr className="border-[0.5px] border-border-light my-8" />
        <h2 className="text-3xl font-bold mb-4">
          Why Advertise on <span className="text-bg-primary">MOFSE?</span>
        </h2>

        {ADVERTISE_DATA.map((item, index) => (
          <ul key={index} className="flex flex-col gap-4 mb-8">
            <li className="flex gap-4 mt-4">
              <picture>
                <img src={item.icon} alt={item.title} />
              </picture>
              <div className="flex flex-col gap-2">
                <p className="text-2xl font-bold text-text-primary">
                  {item.title}
                </p>
                <p className="text-text-secondary">{item.description}</p>
              </div>
            </li>
            <hr className="border-[0.5px] border-border-light" />
          </ul>
        ))}

        <p className="text-2xl font-bold">Got Any Queries</p>
        <p className="text-text-secondary">
          Reach Out to Us via Email{" "}
          <Link href="mailto:<EMAIL>" className="underline">
            <EMAIL>
          </Link>
        </p>
      </section>
      <Footer />
    </>
  );
};

export default Page;
