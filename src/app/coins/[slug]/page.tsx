import CoinDetailsContainer from "@/components/coin-details/CoinDetailsContainer";
import Footer from "@/components/comman/Footer";
import Header from "@/components/comman/Header";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { api } from "@/lib/api";
import { convertToSpaces, convertToKebabCase } from "@/lib/utils";
import { Metadata } from "next";

export type paramsType = Promise<{ slug: string }>;

export async function generateMetadata({
  params,
}: {
  params: paramsType;
}): Promise<Metadata> {
  const { slug } = await params;
  const coinsQuery = await api.coins.getAll(
    "marketCap",
    0,
    10,
    "DESC",
    encodeURIComponent(convertToSpaces(slug))
  );

  const findCoin = coinsQuery.data.coins.find(
    (coin) => convertToKebabCase(coin.name) === slug
  );

  const coinQuery = await api.coins.get(findCoin?.uuid || "");
  const coin = coinQuery.data.coin;

  return {
    title: `${coin.name} (${coin.symbol}) Price, Market Cap & Charts | MOFSE`,
    description: `Get the latest ${coin.name} price, market cap, trading volume, and detailed charts. Track ${coin.symbol}'s real-time value and historical performance on MOFSE.`,
    keywords: [
      `${coin.name}`,
      `${coin.symbol}`,
      "cryptocurrency",
      "price",
      "market cap",
      "charts",
      "crypto trading",
      "MOFSE",
    ],
    openGraph: {
      title: `${coin.name} (${coin.symbol}) Price & Market Data`,
      description: `${coin.name} price: $${parseFloat(coin.price).toFixed(
        2
      )}, Market Cap: $${parseFloat(
        coin.marketCap
      ).toLocaleString()}, 24h Change: ${coin.change}%`,
      images: [{ url: coin.iconUrl }],
    },
    twitter: {
      card: "summary",
      title: `${coin.name} (${coin.symbol}) Price & Market Data`,
      description: `${coin.name} price: $${parseFloat(coin.price).toFixed(
        2
      )}, Market Cap: $${parseFloat(
        coin.marketCap
      ).toLocaleString()}, 24h Change: ${coin.change}%`,
      images: [coin.iconUrl],
    },
  };
}

const Page = async ({ params }: { params: paramsType }) => {
  const { slug } = await params;
  const coinsQuery = await api.coins.getAll(
    "marketCap",
    0,
    10,
    "DESC",
    encodeURIComponent(convertToSpaces(slug))
  );

  const findCoin = coinsQuery.data.coins.find(
    (coin) => convertToKebabCase(coin.name) === slug
  );

  const coinQuery = await api.coins.get(findCoin?.uuid || "");
  const coin = coinQuery.data.coin;

  return (
    <>
      <Header />
      <section className="center py-16">
        <Breadcrumb className="mb-8">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/#assetTable">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href={`/coins/${slug}`}>
                {coin.name}
              </BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <CoinDetailsContainer coin={coin} />
      </section>
      <Footer />
    </>
  );
};

export default Page;
