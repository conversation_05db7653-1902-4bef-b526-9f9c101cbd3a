import CollectionTable from "@/components/collections/CollectionTable";
import Header from "@/components/comman/Header";
import React from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const Page = async () => {
  return (
    <>
      <Header />
      <section className="center py-16">
        <div className="mb-5">
        <Breadcrumb className="mb-2">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/collections">My Collection</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        </div>
        
        <h1 className="text-3xl font-semibold mb-8">My Collection</h1>
        
        <CollectionTable />
      </section>
    </>
  );
};

export default Page;
