import Footer from "@/components/comman/Footer";
import Header from "@/components/comman/Header";
import HTMLContentParser from "@/components/comman/HTMLContentParser";
import ShareButtons from "@/components/comman/ShareButtons";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { api } from "@/lib/api";
import { formatDateTimeInYearMonthDay } from "@/lib/utils";
import { Metadata } from "next";

export type paramsType = Promise<{ slug: string }>;
function getDifficulty(level: string) {
  if (level === "EASY") return "Beginner";
  if (level === "MEDIUM") return "Intermediate";
  if (level === "HARD") return "Advanced";
  return "All";
}

export async function generateMetadata({
  params,
}: {
  params: paramsType;
}): Promise<Metadata> {
  const { slug } = await params;
  const learnBlog = await api.learnBlogs.getById(slug);
  const difficulty = getDifficulty(learnBlog.level);

  return {
    title: learnBlog.title,
    description: learnBlog.content.headersDescription,
    openGraph: {
      title: learnBlog.title,
      description: learnBlog.content.headersDescription,
      images: [{ url: learnBlog.thumbnailUrl }],
      type: "article",
      publishedTime: learnBlog.updatedAt,
      authors: [learnBlog.content.headersDescription],
      tags: [difficulty, "Learning", "Education", "Cryptocurrency"],
    },
    twitter: {
      card: "summary_large_image",
      title: learnBlog.title,
      description: learnBlog.content.headersDescription,
      images: [learnBlog.thumbnailUrl],
    },
  };
}

export default async function Page({ params }: { params: paramsType }) {
  const { slug } = await params;
  const learnBlog = await api.learnBlogs.getById(slug);

  return (
    <section>
      <Header />
      <section className="center py-16">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Learn</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/learn">{learnBlog.title}</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <div className="flex flex-col-reverse md:flex-row gap-8 mt-8">
          <div className="w-full md:w-1/2 lg:w-2/3 flex flex-col gap-6">
            <Badge className="rounded-full text-md px-2 w-28">
              {getDifficulty(learnBlog.level)}
            </Badge>
            <h1 className="text-4xl font-semibold">{learnBlog.title}</h1>
            <p className="text-lg text-text-secondary">
              {" "}
              {learnBlog.content.headersDescription}
            </p>
            <div className="flex gap-4">
              <span>{learnBlog.content.timeTook} Min Read</span>
              <span> | </span>
              <span>
                Last Updated On :{" "}
                {formatDateTimeInYearMonthDay(learnBlog.updatedAt)}
              </span>
            </div>
            <ShareButtons slug={slug} title={learnBlog.title} path="learn" />
          </div>

          <div className="w-full md:w-1/2 lg:w-1/3">
            <picture>
              <img
                src={learnBlog.thumbnailUrl}
                alt={learnBlog.title}
                className="w-full h-96 object-cover rounded-lg"
              />
            </picture>
          </div>
        </div>
        <div className="mt-8 mb-8">
          <h1 className="text-3xl font-semibold mb-4">Content/Learnings:</h1>
          <div className="bg-bg-primary p-8 rounded-lg">
            <ul className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 list-disc gap-4">
              {learnBlog.content.contentLearnings.map((content) => (
                <li key={content}>{content}</li>
              ))}
            </ul>
          </div>
        </div>{" "}
        <HTMLContentParser content={learnBlog.content.body || ""} />
      </section>

      <Footer />
    </section>
  );
}
