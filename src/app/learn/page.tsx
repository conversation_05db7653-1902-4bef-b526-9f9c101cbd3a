import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  B<PERSON><PERSON><PERSON>b<PERSON><PERSON>,
  Bread<PERSON>rumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Header from "@/components/comman/Header";
import { api } from "@/lib/api";
import SearchFilter from "@/components/learn/SearchFilterAndList";
import { Metadata } from "next";
import Footer from "@/components/comman/Footer";

export const metadata: Metadata = {
  title: "MOFSE - Learn",
  description:
    "Explore Topics: Digital Assets can feel complex, but it doesn’t have to be!",
};

const Learn = async () => {
  const categories = await api.categories.getAll("");
  const categoryList = categories.learnCategories;

  return (
    <section>
      <Header />

      <section className="center py-16">
        <Breadcrumb className="mb-2">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/learn">Learn</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <h1 className="text-3xl mb-8">
          <span className="font-semibold">Explore Topics: </span>
          Digital Assets can feel complex, but it doesn’t have to be!
        </h1>

        {/* <p className="text-lg text-text-secondary text-justify mb-8">
          At MOFSE, we believe in transparency and establishing trust with our
          users. We bring you a curated selection of Curated selection of research topics that are making an impact in the crypto ecosystem. Whether it’s next-generation
          blockchain solutions, innovative DeFi platforms, or utility-driven
          tokens, we provide you with carefully selected featured narratives
          that are making real impact
        </p> */}

        <SearchFilter categoryList={categoryList} />
      </section>

      <Footer />
    </section>
  );
};

export default Learn;
