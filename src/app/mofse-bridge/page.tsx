import Footer from "@/components/comman/Footer";
import Header from "@/components/comman/Header";
import Button from "@/components/comman/Button";
import { Metadata } from "next";
import Image from "next/image"; // Import the Image component

export const metadata: Metadata = {
  title: "MOFSE - Enterprise Infrastructure Solutions",
  description: "Comprehensive suite of tools designed to empower businesses and experienced professionals with seamless, efficient solutions tailored to the digital asset economy.",
};

const MofseBridge = async () => {
    return (
        <div className="min-h-screen bg-background">
            <Header />

            {/* Hero Section */}
            <section className="py-16 px-4 max-w-7xl mx-auto">
                <div className="text-center mb-16">
                    <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
                        Enterprise Infrastructure Solutions
                    </h1>
                    <p className="text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                       Power your business with our advanced enterprise analytics and insight enabled banking solutions, designed to meet the complex needs of modern organizations. Through our regulated partners, we provide secure, scalable, and innovative banking tools to streamline your operations and drive growth
                    </p>
                </div>

                <div className="mb-12">
                    <p className="text-muted-foreground leading-relaxed max-w-6xl mx-auto text-justify">
                       MOFSE was founded by a team of seasoned professionals with extensive experience at leading financial institutions, including Lloyds Banking Group, BlackRock, UBS, Silicon Valley Bank, and Deutsche Bank. Leveraging our deep expertise, we are dedicated to addressing the complexities of the digital asset economy, delivering innovative solutions to drive success.
                    </p>
                </div>

                {/* Services Grid */}
                {/* <div className="grid md:grid-cols-2 gap-8 mt-16"> */}
                <div className="flex flex-col md:flex-row gap-8 justify-center items-center md:items-stretch">
                    {/* Portfolio & Risk Management */}
                    <div className="bg-card rounded-lg p-8 border border-border shadow-sm hover:shadow-md transition-shadow max-w-96">
                        <div className="mb-6">
                            {/* Image Placeholder for Portfolio & Risk Management */}
                            <div className="w-full h-48 relative rounded-lg overflow-hidden">
                                <Image
                                    src="/coinrr.png" // Replace with your image path
                                    alt="Mofse Analytics"
                                    layout="fill" // Use fill to make the image cover the parent div
                                    objectFit="cover" // Cover the area, cropping if necessary
                                    className="rounded-lg"
                                />
                            </div>
                        </div>

                        <h3 className="text-xl font-semibold text-foreground mb-4">
                            Mofse Analytics
                        </h3>

                        <p className="text-sm text-muted-foreground leading-relaxed mb-6 text-justify">
                            We offer enterprise analytics solutions integrating onchain and offchain insights to empower your business. Our services deliver actionable data & insights, enabling informed decision-making, identifying key trends, and providing a holistic view of your industry segment.
                        </p>

                        <Button
                            variant="secondary"
                            className="w-full bg-bg-secondary text-text-primary hover:bg-bg-primary hover:text-text-primary"
                        >
                            COMING SOON
                        </Button>
                    </div>

                    {/* Industry Data & Trends */}
                    <div className="bg-card rounded-lg p-8 border border-border shadow-sm hover:shadow-md transition-shadow max-w-96">
                        <div className="mb-6">
                            {/* Image Placeholder for Industry Data & Trends */}
                            <div className="w-full h-48 relative rounded-lg overflow-hidden">
                                <Image
                                    src="/ANA.png" // Replace with your image path
                                    alt="Virtual IBANs & Payment Solutions"
                                    layout="fill"
                                    objectFit="cover"
                                    className="rounded-lg"
                                />
                            </div>
                        </div>

                        <h3 className="text-xl font-semibold text-foreground mb-4">
                            Virtual IBANs & Payment Solutions
                        </h3>

                        <p className="text-sm text-muted-foreground leading-relaxed mb-6 text-justify">
                            Unlock seamless financial operations with our Insight Enabled Banking Solutions.- Virtual IBANs simplify cross-border payments, offering unique account identifiers for efficient transaction tracking and reconciliation. Experience secure, multi-currency payment solutions tailored to your business needs. Streamline global operations with real-time insights and API-driven integration.
                        </p>

                        <Button
                            variant="secondary"
                            className="w-full bg-bg-secondary text-text-primary hover:bg-bg-primary hover:text-text-primary"
                        >
                            COMING SOON
                        </Button>
                    </div>

                    {/* Custom Analytics Development */}
                    {/* <div className="bg-card rounded-lg p-8 border border-border shadow-sm hover:shadow-md transition-shadow">
                        <div className="mb-6">
                           
                            <div className="w-full h-48 relative rounded-lg overflow-hidden">
                                <Image
                                    src="/ass.png"
                                    alt="Mofse Analytics"
                                    layout="fill"
                                    objectFit="cover"
                                    className="rounded-lg"
                                />
                            </div>
                        </div>

                        <h3 className="text-xl font-semibold text-foreground mb-4">
                            Mofse Analytics
                        </h3>

                        <p className="text-sm text-muted-foreground leading-relaxed mb-6 text-justify">
                            We offer comprehensive proprietary data sets, seamlessly integrating onchain and offchain insights to empower your business. Our services deliver actionable data & insights, enabling informed decision-making, identifying key trends, and providing a holistic view of your industry segment.
                        </p>

                        <a href="mailto:<EMAIL>">
                        <Button
                            variant="secondary"
                            className="w-full bg-bg-secondary text-text-primary hover:bg-bg-primary hover:text-text-primary"
                        >
                            Email us
                        </Button>
                        </a>
                        
                    </div> */}
                </div>
            </section>

            <Footer />
        </div>
    );
};

export default MofseBridge;