import Footer from "@/components/comman/Footer";
import Header from "@/components/comman/Header";
import HTMLContentParser from "@/components/comman/HTMLContentParser";
import ShareButtons from "@/components/comman/ShareButtons";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { api } from "@/lib/api";
import { formatDateTimeInYearMonthDay } from "@/lib/utils";
import { Metadata } from "next";

export type paramsType = Promise<{ slug: string }>;

export async function generateMetadata({
  params,
}: {
  params: paramsType;
}): Promise<Metadata> {
  const { slug } = await params;
  const learnBlog = await api.research.getById(slug);

  return {
    title: learnBlog.title,
    description: learnBlog.content.headersDescription,
    openGraph: {
      title: learnBlog.title,
      description: learnBlog.content.headersDescription,
      images: [{ url: learnBlog.thumbnailUrl }],
      type: "article",
      publishedTime: learnBlog.updatedAt,
      authors: [learnBlog.content.headersDescription],
    },
    twitter: {
      card: "summary_large_image",
      title: learnBlog.title,
      description: learnBlog.content.headersDescription,
      images: [learnBlog.thumbnailUrl],
    },
  };
}

export default async function Page({ params }: { params: paramsType }) {
  const { slug } = await params;
  const learnBlog = await api.research.getById(slug);

  return (
    <section>
      <Header />
      <section className="py-16">
        <div className="center">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/research">Research</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href={`/research/${slug}`}>
                  {learnBlog.title}
                </BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <div className="flex flex-col md:flex-row gap-8 mt-8 mb-8">
            <div className="w-full md:w-1/2 lg:w-1/3">
              <picture>
                <img
                  src={learnBlog.thumbnailUrl}
                  alt={learnBlog.title}
                  className="rounded-xl h-96 object-cover w-full"
                />
              </picture>
            </div>
            <div className="w-full md:w-1/2 lg:w-2/3">
              <h1 className="text-4xl font-semibold mb-8">{learnBlog.title}</h1>
              <p className="text-xl mb-8">
                By {learnBlog.content.headersDescription}
              </p>
              <div className="flex gap-8 mb-8">
                <span>{learnBlog.content.timeTook} Min Read</span>
                <span>
                  Last Updated On :{" "}
                  {formatDateTimeInYearMonthDay(learnBlog.updatedAt)}
                </span>
              </div>
              <ShareButtons
                slug={slug}
                title={learnBlog.title}
                path="research"
              />
            </div>
          </div>

          <HTMLContentParser content={learnBlog.content.body || ""} />
        </div>
      </section>
      <Footer />
    </section>
  );
}
