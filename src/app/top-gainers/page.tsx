import Footer from "@/components/comman/Footer";
import Header from "@/components/comman/Header";
import TopGainersTable from "@/components/top-gainers/TopGainersTable";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Metadata } from "next";
import React from "react";

export const metadata: Metadata = {
  title: "MOFSE - Top Gainers",
  description:
    "The list of top gainers based on Market Capitalization. To view charts & more information, please search or lookup the table on Home and click on the asset",
};

const Page = () => {
  return (
    <>
      <Header />
      <section className="center py-16">
        <Breadcrumb className="mb-2">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/top-gainers">Top Gainers</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <h1 className="text-3xl font-semibold mb-2">Top Gainers</h1>
        <p className="text-lg text-text-secondary mb-8">
          Below is a list of top gainers based on Market Capitalization. To view charts & more information, please search or lookup the table on Home and click on the asset
        </p>

        <TopGainersTable />
      </section>
      <Footer />
    </>
  );
};

export default Page;
