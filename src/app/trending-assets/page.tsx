import Footer from "@/components/comman/Footer";
import Header from "@/components/comman/Header";
import TrendingAssetsTable from "@/components/trending-assets/TrendingAssetsTable";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Metadata } from "next";
import React from "react";

export const metadata: Metadata = {
  title: "MOFSE - Trending Assets",
  description:
    "The list of top trending assets based on Market Capitalization. To view charts & more information, please search or lookup the table on Home and click on the asset",
};

const Page = async () => {
  return (
    <>
      <Header />
      <section className="center py-16">
        <Breadcrumb className="mb-2">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/trending-assets">Trending Assets</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <h1 className="text-3xl font-semibold my-2">Trending Assets</h1>
        <p className="text-lg text-text-secondary mb-8">
          Trending Assets
        </p>

        <TrendingAssetsTable />
      </section>
      <Footer />
    </>
  );
};

export default Page;
