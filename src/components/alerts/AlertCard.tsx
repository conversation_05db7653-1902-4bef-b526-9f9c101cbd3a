import React from "react";
import { getAlertEmojis } from "./utils";
import { formatPrice, timeAgo } from "@/lib/utils";
import { BitcoinAlert } from "@/lib/api.interface";

const AlertCard = ({
  item,
  iconUrl,
  symbol,
}: {
  item: BitcoinAlert;
  iconUrl: string;
  symbol: string;
}) => {
  return (
    <li className="flex items-center gap-4 p-4 bg-white rounded-lg border border-border-light hover:shadow-sm transition-shadow mb-2">
      <div className="flex-shrink-0">
        <picture>
          <img src={iconUrl} alt={symbol} height={40} width={40} />
        </picture>
      </div>
      <div className="flex-grow">
        <div className="flex items-center justify-between gap-2 mb-1">
          <span>{getAlertEmojis(item.amountInUsd)}</span>
          <span className="text-sm text-text-secondary">
            {timeAgo(new Date(item.createdAt).getTime())}
          </span>
        </div>
        <p className="font-medium text-text-primary">
          {formatPrice(Math.round(+item.amount))} #{symbol} (
          {formatPrice(Math.round(+item.amountInUsd))} USD)
        </p>
        <p className="text-sm text-text-secondary">
          transferred from unknown to unknown
        </p>
      </div>
    </li>
  );
};

export default AlertCard;
