import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogTrigger,
} from "../ui/dialog";
import { Button } from "../ui/button";
import { TextField } from "../comman/TextField";
import MultipleSelector from "../ui/MultiSelector";
import { Field } from "../comman/Field";
import { Coin } from "@/lib/api.interface";
import { Option } from "../ui/MultiSelector";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import {
  useAlertFilters,
  useCreateAlertsMutation,
  useDeleteAlertMutation,
} from "@/lib/state";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "../ui/badge";
import { X } from "lucide-react";
import { MIN_ALERT_AMOUNT } from "@/lib/constant";

const option = z.object({
  label: z.string(),
  value: z.string(),
});

const alertFormSchema = z.object({
  alertName: z.string().min(1, "Alert name is required"),
  minimumTransferValue: z
    .string()
    .min(
      MIN_ALERT_AMOUNT,
      "Minimum transfer value is required and should be greater than 10 Million"
    ),
  selectedCoins: z.array(option).min(1, "At least one coin is required"),
});

export type AlertFormData = z.infer<typeof alertFormSchema>;

const CreateAlertModal = ({
  open,
  onOpenChange,
}: {
  coins?: Coin[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) => {
  const OPTIONS: Option[] = [
    {
      label: "Bitcoin",
      value: "bitcoin",
    },
    {
      label: "Ethereum",
      value: "ethereum",
    },
    {
      label: "USDC",
      value: "usdc",
    },
    {
      label: "USDT",
      value: "usdt",
    },
  ];

  const form = useForm<AlertFormData>({
    resolver: zodResolver(alertFormSchema),
    defaultValues: {
      alertName: "",
      minimumTransferValue: "0",
      selectedCoins: [],
    },
  });
  const createAlertMutation = useCreateAlertsMutation();
  const deleteAlertMutation = useDeleteAlertMutation();
  const alertFiltersQuery = useAlertFilters();
  const alertFiltersData = alertFiltersQuery.data;

  const handleCreateAlert = async (data: AlertFormData) => {
    const idDuplicateName = alertFiltersData?.find(
      (alert) =>
        alert.alertName.trim().toLowerCase() ===
        data.alertName.trim().toLowerCase()
    );
    if (idDuplicateName) {
      form.setError("alertName", {
        type: "manual",
        message: "Alert name already exists",
      });
      return;
    }
    // store in localstorage
    await createAlertMutation.mutateAsync({
      // id: uuidv4(),
      alertName: data.alertName,
      minimumTransferValue: data.minimumTransferValue,
      selectedCoins: data.selectedCoins.map((coin) => coin.value),
    });
    form.reset();
    toast.success("Alert created successfully");
    onOpenChange(false);
  };

  const handleDeleteAlert = async (alertName: string) => {
    await deleteAlertMutation.mutateAsync(alertName);
  };
 
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger>
        <Button className="mb-4 cursor-pointer">Create Your Own Alert</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Custom Alert Filter</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="create" className="w-full">
          <TabsList>
            <TabsTrigger value="create">Create</TabsTrigger>
            <TabsTrigger value="view">View</TabsTrigger>
          </TabsList>
          <TabsContent value="create" className="space-y-4">
            <Field
              label="Alert Name"
              required
              error={form.formState.errors.alertName?.message}
              control={
                <TextField
                  placeholder="Enter Alert Name"
                  className="h-10"
                  {...form.register("alertName")}
                />
              }
            />

            <Field
              label="Minimum Transfer value"
              required
              error={form.formState.errors.minimumTransferValue?.message}
              control={
                <TextField
                  placeholder="Enter Minimum Transfer value"
                  className="h-10"
                  type="number"
                  {...form.register("minimumTransferValue")}
                />
              }
            />

            <Field
              label="Select Coin"
              required
              error={form.formState.errors.selectedCoins?.message}
              control={
                <MultipleSelector
                  defaultOptions={OPTIONS}
                  value={form.watch("selectedCoins")}
                  placeholder="Select coin..."
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">
                      no results found.
                    </p>
                  }
                  onChange={(value: Option[]) => {
                    form.setValue("selectedCoins", value);
                  }}
                />
              }
            />

            <DialogFooter className="mt-4">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button onClick={form.handleSubmit(handleCreateAlert)}>
                Create Alert
              </Button>
            </DialogFooter>
          </TabsContent>
          <TabsContent value="view">
            {alertFiltersData?.map((item, index) => (
              <div
                key={item.alertName + index}
                className="p-2 border border-border-light rounded mb-2 relative"
              >
                <p>
                  {" "}
                  <span className="font-semibold">Name:</span> {item.alertName}
                </p>
                <p>
                  <span className="font-semibold">Amount:</span>{" "}
                  {item.minimumTransferValue}
                </p>
                <p>
                  <span className="font-semibold">Coins:</span>{" "}
                  {item.selectedCoins.map((coin) => (
                    <Badge key={coin} className="mr-2">
                      {coin}
                    </Badge>
                  ))}
                </p>
                <button
                  className="absolute top-2 right-2 cursor-pointer"
                  onClick={() => handleDeleteAlert(item.alertName)}
                >
                  <X />
                </button>
              </div>
            ))}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default CreateAlertModal;
