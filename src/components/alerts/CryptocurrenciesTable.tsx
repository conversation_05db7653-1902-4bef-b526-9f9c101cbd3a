"use client";

import { Coin } from "@/lib/api.interface";
import { Pagination } from "../comman/Pagination";
import { useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { Triangle } from "lucide-react";
import { useBookmarks, useCoins, useUserLoggedIn } from "@/lib/state";
import SearchBar from "../comman/SearchBar";
import { useDebounce, useScreenSize } from "@/lib/hook";
import Loader from "../comman/Loader";
import { convertToKebabCase, formatPrice, shortenText } from "@/lib/utils";
import Link from "next/link";
import { ConfigProvider, Table, TableProps } from "antd";

// Helper Component: AssetValueChange
function AssetValueChange({ value }: { value: string }) {
  const isNegative = value?.includes("-");
  const updatedValue = isNegative ? value.replace("-", "") : value;

  return (
    <span
      className={`flex items-center text-sm gap-2 ${
        isNegative ? "text-red-500" : "text-green-600"
      }`}
    >
      <Triangle
        className={`w-3 h-3 ${isNegative ? "rotate-180" : "rotate-0"}`}
        fill={isNegative ? "red" : "green"}
      />
      {updatedValue}%
    </span>
  );
}

// Helper Component: Bookmark
function Bookmark({
  coin,
  isBookmarked,
}: {
  coin: Coin;
  isBookmarked: boolean;
}) {
  const {
    createBookmark,
    removeBookmark,
    removeBookmarkIsPending,
    createBookmarkIsPending,
  } = useBookmarks({});
  const isPending = removeBookmarkIsPending || createBookmarkIsPending;
  const isLoggedIn = useUserLoggedIn();
  const router = useRouter();
  const pathname = usePathname();

  return (
    <div className="w-10">
      {isPending ? (
        <Loader />
      ) : (
        <picture>
          <img
            height={24}
            width={24}
            src={isBookmarked ? "/star-filled.svg" : "/star.svg"}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();

              if (!isLoggedIn) {
                router.push(
                  `/login?from=${pathname === "/" ? "/#assetTable" : pathname}`
                );
                return;
              }
              if (isBookmarked) {
                removeBookmark(coin.uuid);
              } else {
                createBookmark(coin.uuid);
              }
            }}
            alt={coin.name}
          />
        </picture>
      )}
    </div>
  );
}


// Main Component: CryptoTable
export function CryptoTable() {
  const router = useRouter();
  const [order, setOrder] = useState("DESC");
  const [orderBy, setOrderBy] = useState("marketCap");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  const [search, setSearch] = useState("");
  const query = useDebounce(search, 500);
  const { width } = useScreenSize();
  const isMobile = width! < 768;

  const cryptoColumns: TableProps<Coin>["columns"] = [
    {
      title: "",
      key: "actions01",
      dataIndex: "isBookmarked",
      width: 45,
      render: (isBookmarked: boolean, coin: Coin) => (
        <Bookmark coin={coin} isBookmarked={isBookmarked} />
      ),
    },
    {
      title: "#",
      key: "index",
      fixed: "left",
      dataIndex: "index",
      width: 50,
      render: (text: string) => <span className="text-sm text-text-primary">{text}</span>,
    },
    {
      title: "Asset",
      dataIndex: "name",
      key: "name",
      fixed: "left",
      align: "left",
      width: isMobile ? 100 : 200,
      render: (text: string, coin) => (
        <Link href={`/coins/${convertToKebabCase(text)}`}>
          <div className="flex gap-2">
            <picture>
              <img height={24} width={24} src={coin.iconUrl} alt={coin.name} />
            </picture>
            <span className="hidden md:block text-base font-medium text-text-primary">
              {shortenText(text, 15)}{" "}
              <span className="text-sm text-text-primary">({coin.symbol})</span>
            </span>
          </div>
        </Link>
      ),
    },
    {
      title: "Prices",
      dataIndex: "price",
      key: "price",
      width: 120,
      align: "left",
      sorter: true,
      render: (text: string) => (
        <span className="text-sm text-text-primary">${formatPrice(parseFloat(text))}</span>
      ),
    },
    {
      title: "1h",
      dataIndex: "change_1h",
      key: "change_1h",
      sorter: true,
      align: "left",
      width: 100,
      render: (text: string) => <AssetValueChange value={text} />,
    },
    {
      title: "24h",
      dataIndex: "change_24h",
      key: "change_24h",
      sorter: true,
      align: "left",
      width: 100,
      render: (text: string) => <AssetValueChange value={text} />,
    },
    {
      title: "7d",
      dataIndex: "change_7d",
      key: "change_7d",
      sorter: true,
      align: "left",
      width: 100,
      render: (text: string) => <AssetValueChange value={text} />,
    },
    {
      title: "24h Volume",
      dataIndex: "24hVolume",
      key: "24hVolume",
      sorter: true,
      align: "left",
      width: 150,
      render: (text: string) => (
        <span className="text-sm text-text-primary">${parseFloat(text).toLocaleString()}</span>
      ),
    },
    {
      title: "Market Cap",
      dataIndex: "marketCap",
      key: "marketCap",
      sorter: true,
      align: "left",
      width: 150,
      render: (text: string) => (
        <span className="text-sm text-text-primary">${parseFloat(text).toLocaleString()}</span>
      ),
    },
    {
        title: (
            <div className="flex flex-col items-center">
                <span>Dominance</span>
                <span>(Market Cap)</span>
            </div>
      ),
      dataIndex: "dominance",
      key: "dominance",
      width: 150,
      align: "center",
      render: (text: string) => (
        <span className="text-sm text-text-primary text-center">{text}%</span>
      ),
    },
  ];
  
  const coinsQuery = useCoins({
    orderBy,
    offset: page === 1 ? 0 : +page * 10,
    limit: +limit,
    order,
    searchText: query,
    isStableCoinsEnabled: false, // Hardcoded for this component
  });

  const { userBookmarksData } = useBookmarks({});
  const bookmarkedCoins = userBookmarksData?.coins[0]?.map((coin) => coin.uuid);
  const coinList = coinsQuery.data?.data.coins?.map((coin, index) => ({
    ...coin,
    index: (+page - 1) * 20 + (index + 1),
    isBookmarked: bookmarkedCoins?.includes(coin.uuid),
  }));
  const total = coinsQuery.data?.data.stats.total || 10;

  const handlePagination = (page: number) => {
    setPage(page);
    router.push(`/#assetTable`);
  };

  const handlePageSizeChange = (value: string) => {
    setLimit(+value);
    router.push(`/#assetTable`);
  };

  return (
    <div style={{ minHeight: '600px' }}>
      <div className="mb-4 flex justify-end">
        <SearchBar handleSearch={setSearch} placeholder="Search Crypto..." />
      </div>
      <ConfigProvider
        theme={{
          components: {
            Spin: { colorPrimary: "#bbd955" },
            Table: { fontFamily: "DM Sans", colorPrimary: "#bbd955" },
          },
        }}
      >
        <div className="overflow-x-auto">
        <Table
          columns={cryptoColumns}
          dataSource={coinList || []}
          pagination={false}
          onChange={(_p, _f, s: any) => {
            setOrder(s.order === "ascend" ? "ASC" : "DESC");
            setOrderBy(s.columnKey!);
          }}
          scroll={{ x: "600px" }}
          loading={coinsQuery.isLoading}
          rowKey="uuid"
          onRow={(record) => ({
            onClick: () => router.push(`/coins/${convertToKebabCase(record.name)}`),
          })}
          className="crypto-table"
        />
        </div>
      </ConfigProvider>
      <div className="mt-4">
        <Pagination
          totalPages={total}
          currentPage={page}
          onPageChange={handlePagination}
          pageSizeOptions={[
            { label: "20", value: "20" },
            { label: "50", value: "50" },
            { label: "100", value: "100" },
          ]}
          onPageSizeChange={handlePageSizeChange}
        />
      </div>
    </div>
  );
}