"use client";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>spons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "recharts";
import { formatChartDate } from "@/lib/utils";
import Image from "next/image";
import { formatTooltip } from "../mofse-advance/utils";
import { Coin } from "@/lib/api.interface";

// Default chart colors - Bitcoin specific
const DEFAULT_COLORS = {
  BORDER: "#409E13",
  DARK: "#62A442",
  FILL: "#CAF2B6",
  WHITE: "#FFFFFF",
};

// Function to get coin-specific colors
function getCoinColors(coin?: string) {
  switch (coin?.toUpperCase()) {
    case "BTC":
    case "BITCOIN":
      return {
        FILL: "#f7931a",
        BORDER: "#e6830f",
      };
    case "ETH":
    case "ETHEREUM":
      return {
        FILL: "#8c8c8c",
        BORDER: "#2A2A2B",
      };
    case "USDT":
      return {
        FILL: "#26A17B",
        BORDER: "#1e8a66",
      };
    case "USDC":
      return {
        FILL: "#2775CA",
        BORDER: "#1f5fa3",
      };
    case "BNB": 
      return {
        FILL: "#f1b90a",
        BORDER: "#ffce36",
      };
    case "SOL":
      return {
        FILL: "#9945FF",
        BORDER: "#7a37cc",
      };
    default:
      return {
        FILL: DEFAULT_COLORS.FILL,
        BORDER: DEFAULT_COLORS.BORDER,
      };
  }
}

// Data point type for the chart
export type DataPoint = {
  date: string;
  count: number;
  timestamp: number;
};

type ChartPropTypes = {
  data: DataPoint[];
  coin?: Coin | null;
  chartType?: "bitcoin" | "other";
  showDate?: boolean;
  onChainFilter?: string;
};

const TransactionCountHistoryBarGraph = ({
  data,
  coin,
  showDate,
  onChainFilter = 'transaction-count'
}: ChartPropTypes) => {
  const colors = getCoinColors(coin?.symbol);

  return (
    <div style={{ width: "100%", height: 400 , position: "relative"}}>
      <Image
        src="/MOFSE.svg"
        width={140}
        height={40}
        alt="Watermark"
        style={{
          zIndex: '1',
          position: "absolute",
          top: "20px", 
          right: "110px",
          opacity: 0.6,
          pointerEvents: "none", 
          filter: "grayscale(100%)"
        }}
      />
      <ResponsiveContainer width="100%" height={400}>
        <BarChart
          data={data}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
          <XAxis
            dataKey="date"
            tick={{ fontSize: 10 }}
            tickFormatter={(value) => formatChartDate(value, { showDate })}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            orientation="right"
            domain={[0, "dataMax"]}
            tickFormatter={(tick) => `${tick / 1000}k`}
            tick={{ fontSize: 10 }}
            tickLine={false}
            axisLine={false}
          />
          <Tooltip
            formatter={(value) => formatTooltip(value as string, onChainFilter)}
          />
          <Bar
            dataKey="count"
            fill={colors.FILL}
            stroke={colors.BORDER}
            strokeWidth={1}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default TransactionCountHistoryBarGraph;
