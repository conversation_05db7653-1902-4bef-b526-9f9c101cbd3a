"use client";
import React, { useEffect, useState } from "react";
import Button from "../comman/Button";
import { toast } from "sonner";
import { auth } from "@/config/firebase";
import { api } from "@/lib/api";

const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

const ConfirmationForm = () => {
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [isSendingEmail, setIsSendingEmail] = useState<boolean>(false);
  const [isContinueLoading, setIsContinueLoading] = useState<boolean>(false);
  const COOLDOWN_TIME = 120; // 2 minutes in seconds

  useEffect(() => {
    if (timeLeft > 0) {
      const timerId = setInterval(() => {
        setTimeLeft((time) => time - 1);
      }, 1000);
      return () => clearInterval(timerId);
    }
  }, [timeLeft]);

  const handleContinue = async () => {
    setIsContinueLoading(true);
    if (!auth.currentUser) {
      toast.error("No user is currently logged in.");
      return;
    }

    await auth.currentUser.reload();

    if (auth.currentUser.emailVerified) {
      await api.users.onboarding({ email: auth.currentUser.email });
      window.location.href = "/";
    } else {
      toast.error("Please check your inbox and verify your email.");
    }

    setIsContinueLoading(false);
  };

  const handleResend = async () => {
    setIsSendingEmail(true);
    try {
      await api.auth.verifyEmail(auth.currentUser?.email as string);
      setTimeLeft(COOLDOWN_TIME);
      toast.success("Verification email sent successfully!");
    } catch (error) {
      console.error(error);
      toast.error("Failed to send verification email");
    }
    setIsSendingEmail(false);
  };

  return (
    <div className="w-full md:w-1/2 flex flex-col gap-4 items-center  p-8 border border-border-light rounded-lg">
      <picture>
        <img src="/envelope.svg" alt="" />
      </picture>
      <p>We have sent you verification email. Please check your inbox.</p>
      <Button
        className="w-64"
        onClick={handleContinue}
        disabled={isContinueLoading}
        loading={isContinueLoading}
      >
        Continue
      </Button>
      <Button
        className="w-64"
        variant="outline"
        onClick={handleResend}
        disabled={isSendingEmail}
        loading={isSendingEmail}
      >
        {timeLeft > 0 ? `Resend (${formatTime(timeLeft)})` : "Resend"}
      </Button>
    </div>
  );
};

export default ConfirmationForm;
