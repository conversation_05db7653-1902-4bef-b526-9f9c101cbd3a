"use client";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Field } from "../comman/Field";
import { TextField } from "../comman/TextField";
import Button from "../comman/Button";
import { api } from "@/lib/api";
import { toast } from "sonner";
import { useSearchParams } from "next/navigation";

const createPasswordFormSchema = z
  .object({
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[0-9]/, "Password must contain at least one number")
      .regex(
        /[@$!%*?&]/,
        "Password must contain at least one special character (@$!%*?&)"
      ),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

type CreatePasswordFormData = z.infer<typeof createPasswordFormSchema>;

const CreatePasswordForm = () => {
  const token = useSearchParams().get("token");
  const [isCreatePasswordPending, setIsCreatePasswordPending] = useState(false);
  const form = useForm<CreatePasswordFormData>({
    resolver: zodResolver(createPasswordFormSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (data: CreatePasswordFormData) => {
    setIsCreatePasswordPending(true);
    try {
      await api.auth.updatePassword(data.password, token!);
      window.location.href = "/login";
    } catch (error) {
      const errorMessage = (
        error as { response: { data: { message: string } } }
      ).response.data.message;

      toast.error(errorMessage);
    }
    setIsCreatePasswordPending(false);
  };

  return (
    <div>
      <h2 className="text-center text-3xl font-semibold mb-4">
        Create New Password
      </h2>
      <p className="text-center text-text-secondary">
        Your new password must be different from <br />
        previous used passwords
      </p>
      <form
        noValidate
        className="space-y-4"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <Field
          label="New Password"
          required
          error={form.formState.errors.password?.message}
          control={
            <TextField.Password
              placeholder="New Password"
              {...form.register("password")}
            />
          }
        />
        <Field
          label="Confirm Password"
          required
          error={form.formState.errors.confirmPassword?.message}
          control={
            <TextField.Password
              placeholder="Confirm Password"
              {...form.register("confirmPassword")}
            />
          }
        />
        <Button
          type="submit"
          className="w-full font-bold text-lg"
          disabled={isCreatePasswordPending}
          loading={isCreatePasswordPending}
        >
          Create Password
        </Button>
      </form>
    </div>
  );
};

export default CreatePasswordForm;
