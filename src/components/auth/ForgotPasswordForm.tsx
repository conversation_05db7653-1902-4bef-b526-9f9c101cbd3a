"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Field } from "../comman/Field";
import { TextField } from "../comman/TextField";
import Button from "../comman/Button";
import { useState } from "react";
import Link from "next/link";
import { api } from "@/lib/api";
import { toast } from "sonner";

const forgotPasswordFormSchema = z.object({
  email: z.string().email("Invalid email"),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordFormSchema>;

const ForgotPasswordForm = () => {
  const [isSendingMail, setIsSendingMail] = useState(false);
  const form = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordFormSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setIsSendingMail(true);
    try {
      await api.auth.forgotPassword(data.email);
      toast.success("Password reset link sent successfully");
    } catch (error) {
      const errorMessage = (error as { message: string }).message;
      console.log(errorMessage);
      form.setError("email", {
        message: "Account with this email does not exist.",
      });
    }
    setIsSendingMail(false);
  };

  return (
    <div>
      <h2 className="text-3xl text-center font-semibold mb-4">
        Forgot Password?
      </h2>
      <p className="text-center text-text-secondary mb-6">
        Enter your email to we will send further instructions on email
      </p>
      <form
        noValidate
        className="space-y-4"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <Field
          label="Email"
          required
          error={form.formState.errors.email?.message}
          control={
            <TextField placeholder="Email" {...form.register("email")} />
          }
        />

        <Button
          type="submit"
          className="w-full font-bold text-lg"
          disabled={isSendingMail}
          loading={isSendingMail}
        >
          Send Instructions
        </Button>
      </form>
      <p className="text-center mt-4">
        <Link href="/login">Back to Login</Link>
      </p>
    </div>
  );
};

export default ForgotPasswordForm;
