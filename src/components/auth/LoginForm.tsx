"use client";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Field } from "../comman/Field";
import { TextField } from "../comman/TextField";
import Link from "next/link";
import Button from "../comman/Button";
import { api } from "@/lib/api";
import { toast } from "sonner";
import { auth } from "@/config/firebase";
import { signInWithEmailAndPassword } from "firebase/auth";
import { SignInWithGoogle } from "./utils";
import { useSearchParams } from "next/navigation";
import { STORAGE_KEYS } from "@/lib/constant";

const loginFormSchema = z.object({
  email: z.string().email("Invalid email"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number")
    .regex(
      /[@$!%*?&]/,
      "Password must contain at least one special character (@$!%*?&)"
    ),
});

type LoginFormData = z.infer<typeof loginFormSchema>;

const LoginForm = () => {
  const [isLoginPending, setIsLoginPending] = useState(false);
  const [isSignWithGooglePending, setIsSignWithGooglePending] = useState(false);
  const searchParams = useSearchParams();
  const from = searchParams.get("from");

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoginPending(true);
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        data.email,
        data.password
      );

      const user = userCredential.user;

      if (!user.emailVerified) {
        await api.auth.verifyEmail(data.email);
        window.location.href = "/confirm-verification";
        return;
      }

      const userData = await api.users.profile();

      if (userData.status !== "ACTIVE") {
        form.setError("email", {
          message: "User is blocked",
        });
        return;
      }

      if (userData.role === "ADMIN") {
        form.setError("email", {
          message: "Invalid User",
        });

        return;
      }

      localStorage.setItem(
        STORAGE_KEYS.LOGGED_IN_TIME,
        new Date().getTime().toString()
      );

      window.location.href = from || "/";
      return user;
    } catch (error: unknown) {
      const errorMessage = (error as { message: string }).message;
      if (errorMessage.includes("auth/user-disabled")) {
        form.setError("email", {
          message: "User is blocked",
        });
      } else if (errorMessage.includes("auth/invalid-credential")) {
        form.setError("email", {
          message: "Invalid credentials",
        });
      } else {
        toast.error(errorMessage);
      }
    }
    setIsLoginPending(false);
  };

  const handleGoogleSignIn = async () => {
    setIsSignWithGooglePending(true);
    await SignInWithGoogle(from || "/");
    setIsSignWithGooglePending(false);
  };

  return (
    <div>
      <h2 className="text-center text-3xl font-semibold mb-4">Login</h2>
      <form
        noValidate
        className="space-y-4"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <Field
          label="Email"
          required
          error={form.formState.errors.email?.message}
          control={
            <TextField placeholder="Email" {...form.register("email")} />
          }
        />
        <Field
          label="Password"
          required
          error={form.formState.errors.password?.message}
          control={
            <TextField.Password
              placeholder="Password"
              {...form.register("password")}
            />
          }
        />

        <p className="text-right text-sm text-text-secondary">
          <Link href="/forgot-password" className="font-semibold">
            Forgot Password?
          </Link>
        </p>

        <Button
          type="submit"
          className="w-full font-bold text-lg"
          disabled={isLoginPending}
          loading={isLoginPending}
        >
          Login
        </Button>
      </form>

      <p className="text-center text-base mt-4">
        Don&apos;t have an account?{" "}
        <Link href="/sign-up" className="font-semibold">
          Sign Up
        </Link>
      </p>
      <p className="text-center text-base my-4">Or Continue with</p>
      <div className="w-full flex justify-center items-center">
        <Button
          variant="outline"
          prefixIcon={
            <picture>
              <img src="/google-icon.svg" alt="" className="w-6 h-6" />
            </picture>
          }
          className="w-full"
          onClick={handleGoogleSignIn}
          disabled={isSignWithGooglePending}
          loading={isSignWithGooglePending}
        >
          Continue with Google
        </Button>
      </div>
    </div>
  );
};

export default LoginForm;
