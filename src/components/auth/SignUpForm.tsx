"use client";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Field } from "../comman/Field";
import { TextField } from "../comman/TextField";
import Button from "../comman/Button";
import Link from "next/link";
import { api } from "@/lib/api";
import { toast } from "sonner";
import { createUserWithEmailAndPassword } from "firebase/auth";
import { auth } from "@/config/firebase";
// import { SignInWithGoogle } from "./utils";
import { STORAGE_KEYS } from "@/lib/constant";

const signUpFormSchema = z.object({
  name: z.string().min(1, "Name must be at least 1 character"),
  companyName: z.string().optional(),
  country: z.string().optional(),
  email: z.string().email("Invalid email"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number")
    .regex(
      /[@$!%*?&]/,
      "Password must contain at least one special character (@$!%*?&)"
    ),
});

type SignUpFormData = z.infer<typeof signUpFormSchema>;

const SignUpForm = () => {
  const [isSignUpPending, setIsSignUpPending] = useState(false);
  // const [isSignWithGooglePending, setIsSignWithGooglePending] = useState(false);
  const form = useForm<SignUpFormData>({
    resolver: zodResolver(signUpFormSchema),
    defaultValues: {
      email: "",
      password: "",
      name: "",
      companyName: "",
      country: "",
    },
  });

  const onSubmit = async (data: SignUpFormData) => {
    setIsSignUpPending(true);
    try {
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        data.email,
        data.password
      );
      if (userCredential.user) {
        await api.auth.verifyEmail(data.email);

        await api.users.create({
          email: data.email,
          username: data.name,
          isGoogle: false,
          companyName: data.companyName,
          country: data.country,
        });

        localStorage.setItem(
          STORAGE_KEYS.LOGGED_IN_TIME,
          new Date().getTime().toString()
        );

        window.location.href = "/confirm-verification";
      }
    } catch (error) {
      const errorMessage = (error as { message: string }).message;
      if (errorMessage.includes("(auth/email-already-in-use)")) {
        form.setError("email", {
          message: "Account with this email already exists!",
        });
      } else {
        toast.error(errorMessage);
      }
    }
    setIsSignUpPending(false);
  };

  // const handleGoogleSignIn = async () => {
  //   setIsSignWithGooglePending(true);
  //   await SignInWithGoogle("/");
  //   setIsSignWithGooglePending(false);
  // };

  return (
    <div>
      <h2 className="text-center text-3xl font-semibold mb-4">Free Trial</h2>
      <form
        noValidate
        className="space-y-4"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <Field
          label="Name"
          required
          error={form.formState.errors.name?.message}
          control={<TextField placeholder="Name" {...form.register("name")} />}
        />
        <Field
          label="Company name"
          control={
            <TextField
              placeholder="Company name"
              {...form.register("companyName")}
            />
          }
        />

        <Field
          label="Country"
          control={
            <TextField placeholder="Country" {...form.register("country")} />
          }
        />

        <Field
          label="Email"
          required
          error={form.formState.errors.email?.message}
          control={
            <TextField placeholder="Email" {...form.register("email")} />
          }
        />
        <Field
          label="Password"
          required
          error={form.formState.errors.password?.message}
          control={
            <TextField.Password
              placeholder="Password"
              {...form.register("password")}
            />
          }
        />
        <Button
          type="submit"
          className="w-full font-bold text-lg"
          disabled={isSignUpPending}
          loading={isSignUpPending}
        >
          Free Trial
        </Button>
      </form>

      <p className="text-center text-base mt-4">
        Already have an account?{" "}
        <Link href="/login" className="font-semibold">
          Login
        </Link>
      </p>
      {/* <p className="text-center text-base my-4">Or Continue with</p>
      <div className="w-full flex justify-center items-center">
        <Button
          variant="outline"
          prefixIcon={
            <picture>
              <img src="/google-icon.svg" alt="" className="w-6 h-6" />
            </picture>
          }
          className="w-full"
          onClick={handleGoogleSignIn}
          disabled={isSignWithGooglePending}
          loading={isSignWithGooglePending}
        >
          Continue with Google
        </Button>
      </div> */}
    </div>
  );
};

export default SignUpForm;
