import { api } from "@/lib/api";
import { STORAGE_KEYS } from "@/lib/constant";
import { toast } from "sonner";

export const SignInWithGoogle = async (path: string) => {
  try {
    const res = await api.firebase.signInWithGoogle();
    const user = res.user;

    if (user) {
      const isFirstTime =
        user.metadata.creationTime === user.metadata.lastSignInTime;

      if (isFirstTime) {
        const body = {
          email: user.email ?? "",
          username: (user.displayName || user.email) ?? "",
          profilePicUrl: user.photoURL || "",
          isGoogle: true,
        };
        await api.users.create(body);
      }

      const userData = await api.users.profile();

      if (userData.status !== "ACTIVE") {
        toast.error("User is blocked");
      }

      localStorage.setItem(
        STORAGE_KEYS.LOGGED_IN_TIME,
        new Date().getTime().toString()
      );
      window.location.href = path || "/";
    }
  } catch (error) {
    const errorMessage = (error as { message: string }).message;
    if (errorMessage.includes("auth/user-disabled")) {
      toast.error("User is blocked");
    } else {
      toast.error("Something went wrong");
    }
  }
};
