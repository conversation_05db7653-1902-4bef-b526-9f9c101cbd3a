import { FILTER_LIST } from "../mofse-advance/constant";
import type { ComponentType } from 'react';
import CbdcTable from "./CbdcTable";


interface AssetContentProps {
  filter: string;
}

const chartComponentMap: Record<string, ComponentType<any>> = {
};

const AssetContent: React.FC<AssetContentProps> = ({ filter }) => {

  function getHeading(filter: string) {
    switch (filter) {
        case FILTER_LIST.CBDC:
        return "CBDC Tracker";
        default:
        return "";
  }
}

const ChartToRender = chartComponentMap[filter];

  return (
    <div className="flex">
      <div className="flex-1 py-6 pl-6">
        <div className="flex items-center gap-4 mb-6">
          <h2 className="text-lg font-medium text-gray-900">
           {getHeading(filter)}
          </h2>
        </div>
        <div className="space-y-6">
          {ChartToRender ? (
            <ChartToRender />
          ) : (
            <CbdcTable/>
          )}
        </div>
      </div>
    </div>
  );
};

export default AssetContent;
