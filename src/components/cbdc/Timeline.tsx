"use client";

import { TimelineChange, TimelineData } from "@/lib/api.interface";
import React from "react";


interface TimelineProps {
  data: TimelineData;
  title?: string;
  onClose?: () => void;
}

const Timeline: React.FC<TimelineProps> = ({ data, title }) => {
  const getMonthName = (month: number): string => {
    const months = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];
    return months[month - 1] || "";
  };

  const getLastUpdateDate = (): string => {
    if (!data.content || data.content.length === 0) return "";
    const latest = data.content[0]; // Assuming data is sorted by date desc
    return `${getMonthName(latest.month)} ${latest.year}`;
  };

  const getChangeTypeInfo = (change: TimelineChange) => {
    if (change.valueOld === null && change.valueNew !== null) {
      return {
        type: "Added data",
        bgColor: "bg-green-50",
        textColor: "text-green-600",
        icon: "+"
      };
    } else if (change.valueOld !== null && change.valueNew !== null) {
      return {
        type: "Updated data",
        bgColor: "bg-green-50",
        textColor: "text-green-600",
        icon: "↻"
      };
    } else if (change.valueOld !== null && change.valueNew === null) {
      return {
        type: "Removed data",
        bgColor: "bg-red-50",
        textColor: "text-red-600",
        icon: "−"
      };
    } else {
      return {
        type: "Status updated",
        bgColor: "bg-purple-50",
        textColor: "text-purple-600",
        icon: "◦"
      };
    }
  };

  const renderChangeValue = (value: string | null, isOld: boolean = false) => {
    if (value === null) return null;

    // Handle URLs
    if (value.startsWith('http')) {
      return (
        <a
          href={value}
          target="_blank"
          rel="noopener noreferrer"
          className="hover:underline break-all"
          style={{ color: '#bbd955' }}
        >
          {value}
        </a>
      );
    }

    // Handle status changes
    if (isOld && value) {
      return <span className="text-gray-500">{value} →</span>;
    }

    return <span>{value}</span>;
  };

 return (
    // The root div now just structures the layout over a specific height
    // without any card styling. The modal's rounding will be handled by DialogContent.
    <div className="w-full flex flex-col h-[80vh] overflow-hidden rounded-md">
      {/* Header */}
      <div className="px-6 py-4 relative flex-shrink-0 " style={{ backgroundColor: '#bbd955' }}>
        <h2 className="text-xl font-semibold text-white">
          {title || "Timeline"}
        </h2>
        <p className="text-white/80 text-sm mt-1">
          Last update: {getLastUpdateDate()}
        </p>
      </div>

      {/* Timeline Content (Scrollable Area) */}
      {/* This div now has the white background and will scroll */}
      <div className="flex-1 p-6 space-y-6 overflow-y-auto bg-white">
        {data.content.map((entry, entryIndex) => (
          <div key={entryIndex} className="relative">
            {/* Timeline dot and line */}
            <div className="flex items-start">
              <div className="flex flex-col items-center mr-4">
                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: '#bbd955' }}></div>
                {entryIndex < data.content.length - 1 && (
                  <div className="w-0.5 h-full bg-gray-200 mt-2"></div>
                )}
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-gray-900 mb-3">
                  {getMonthName(entry.month)} {entry.year}
                </h3>

                {entry.tags.map((tagData, tagIndex) => (
                  <div key={tagIndex} className="space-y-3 mb-4">
                    {tagData.changes.map((change, changeIndex) => {
                      const changeInfo = getChangeTypeInfo(change);

                      return (
                        <div key={changeIndex} className="space-y-2">
                          {/* Change type badge */}
                          <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${changeInfo.bgColor} ${changeInfo.textColor}`}>
                            <span className="mr-2">{changeInfo.icon}</span>
                            {changeInfo.type}
                          </div>

                          {/* Change details */}
                          <div className="text-sm text-gray-700 space-y-1 pl-1">
                            {change.property === "description" && (
                              <div>
                                <span className="font-medium">Description: </span>
                                {change.valueOld && (
                                  <div className="text-gray-500 mb-1">
                                    {change.valueOld.substring(0, 100)}...
                                  </div>
                                )}
                                {change.valueNew && (
                                  <div>
                                    {change.valueNew.substring(0, 100)}...
                                  </div>
                                )}
                              </div>
                            )}

                            {change.property === "status" && (
                              <div>
                                <span className="text-gray-500">{change.valueOld}</span>
                                <span className="mx-2">→</span>
                                <span className="font-medium">{change.valueNew}</span>
                              </div>
                            )}

                            {change.property === "announcementLink" && (
                              <div>
                                <span className="font-medium">Link to announcement:</span>
                                <div className="mt-1">
                                  {change.valueNew?.split(/[\n\r]+/).map((link, linkIndex) => (
                                    link.trim() && (
                                      <div key={linkIndex}>
                                        <a
                                          href={link.trim()}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="hover:underline text-xs break-all"
                                          style={{ color: '#bbd955' }}
                                        >
                                          {link.trim()}
                                        </a>
                                      </div>
                                    )
                                  ))}
                                </div>
                              </div>
                            )}

                            {change.property === "whitepaperLink" && (
                              <div>
                                <span className="font-medium">Link to whitepaper:</span>
                                <div className="mt-1">
                                  <a
                                    href={change.valueNew || ""}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:underline text-xs break-all"
                                    style={{ color: '#bbd955' }}
                                  >
                                    {change.valueNew}
                                  </a>
                                </div>
                              </div>
                            )}

                            {change.property === "governanceStructure" && (
                              <div>
                                <span className="font-medium">Governance structure: </span>
                                <span>{change.valueNew}</span>
                              </div>
                            )}

                            {!["description", "status", "announcementLink", "whitepaperLink", "governanceStructure"].includes(change.property) && (
                              <div>
                                <span className="font-medium capitalize">{change.property.replace(/([A-Z])/g, ' $1').toLowerCase()}: </span>
                                {renderChangeValue(change.valueOld, true)} {renderChangeValue(change.valueNew)}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}

                    {/* Tag description */}
                    {tagData.description && (
                      <div className="text-sm text-gray-600 mt-3 p-3 bg-gray-50 rounded">
                        {tagData.description}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Timeline;