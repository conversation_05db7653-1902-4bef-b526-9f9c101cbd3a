"use client";

import { Coin } from "@/lib/api.interface";
import { useCoinPriceHistory } from "@/lib/state";
import { TimePeriod } from "@/lib/types";
import React, { useEffect, useMemo, useState } from "react";
import CoinPriceHistory<PERSON>hart, { DataPoint } from "./CoinPriceHistoryChart";
import { ToggleGroup, ToggleGroupItem } from "../ui/toggle-group";
import Loader from "../comman/Loader";
import { cn, formatDateTimeInYearMonthDay } from "@/lib/utils";

const timePeriodOptions: TimePeriod[] = ["24h", "7d", "30d", "3m", "1y"];

const CoinChart = ({
  coin,
  timePeriodSetter,
}: {
  coin: Coin;
  timePeriodSetter?: (value: TimePeriod) => void;
}) => {
  const [timePeriod, setTimePeriod] = useState<TimePeriod>("24h");
  const cointPriceHistoryQuery = useCoinPriceHistory(coin.uuid, timePeriod);
  const coinPriceHistory = cointPriceHistoryQuery.data?.data.history;
  const coinPrices = useMemo(
    () =>
      coinPriceHistory
        ?.map((entry) => ({
          time: entry.timestamp.toString(),
          price: +entry.price,
        }))
        .reverse(),
    [coinPriceHistory]
  );
  const showTime = timePeriod === "24h";
  const showDate = ["24h", "7d", "30d", "3m"].includes(timePeriod);

  useEffect(() => {
    if (timePeriodSetter) {
      timePeriodSetter(timePeriod);
    }
  }, [timePeriod]);

  return (
    <>
      <div className="flex flex-col items-end gap-4 mb-4">
        <div className="flex justify-center w-full max-w-md">
          <ToggleGroup
            type="single"
            value={timePeriod}
            onValueChange={(value) =>
              value && setTimePeriod(value as TimePeriod)
            }
            className="flex w-full rounded-lg overflow-hidden bg-white border border-border-light"
          >
            {timePeriodOptions.map((option) => (
              <ToggleGroupItem
                key={option}
                value={option}
                className={cn(
                  "flex-1 text-center py-2 font-medium text-sm transition-colors bg-white text-text-primary w-fit"
                )}
              >
                {option}
              </ToggleGroupItem>
            ))}
          </ToggleGroup>
        </div>

        <span className="border border-border-light rounded-lg p-2 text-text-primary font-medium">
          {formatDateTimeInYearMonthDay(new Date().toDateString())}
        </span>
      </div>

      {cointPriceHistoryQuery.isLoading ? (
        <Loader />
      ) : (
        <CoinPriceHistoryChart
          data={coinPrices as DataPoint[]}
          id={coin.uuid}
          showTime={showTime}
          showDate={showDate}
        />
      )}
    </>
  );
};

export default CoinChart;
