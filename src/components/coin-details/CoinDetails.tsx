import { Coin } from "@/lib/api.interface";
import { formatEpochDateTime, formatPrice, shortenText } from "@/lib/utils";
import Link from "next/link";
import React from "react";
import Coin<PERSON><PERSON> from "./CoinChart";
import { Triangle } from "lucide-react";

export function AssetValueChange({ value }: { value: string }) {
  const isNegative = value.includes("-");
  const updatedValue = isNegative ? value.replace("-", "") : value;

  return (
    <span
      className={`flex items-center text-xl gap-2 ${
        isNegative ? "text-red-500" : "text-green-600"
      }`}
    >
      <Triangle
        className={`w-3 h-3 ${isNegative ? "rotate-180" : "rotate-0"}`}
        fill={isNegative ? "red" : "green"}
      />
      {updatedValue}%
    </span>
  );
}

const CoinDetails = ({
  coin,
  allTimeHigh,
  allTimeLow,
  isLoading,
}: {
  coin: Coin;
  allTimeHigh: { time: number; price: number };
  allTimeLow: { time: number; price: number };
  isLoading: boolean;
}) => {
  return (
    <>
      <div className="flex items-center gap-3">
        <picture>
          <img src={coin.iconUrl} alt={coin.name} className="h-6 w-6" />
        </picture>
        <span className="text-2xl font-semibold text-text-primary">
          {coin.name}
        </span>
        <span className="text-text-secondary">{coin.symbol}</span>
        <span className="text-text-secondary">#{coin.rank}</span>
      </div>
      <p className="flex items-center justify-between my-4">
        <span className="text-2xl text-text-primary font-bold">
          ${formatPrice(parseFloat(coin.price))}
        </span>
        <AssetValueChange value={coin.change} />
      </p>
      <hr className="border-[0.5px] border-border-light md:block hidden" />
      <div className="md:hidden">
        <CoinChart coin={coin} />
      </div>
      <p className="mt-4 mb-2 text-text-primary font-medium text-lg">Info</p>
      <p className="flex items-center justify-between p-2 border border-border-light rounded-lg">
        <span>Website</span>
        <Link
          href={coin.websiteUrl}
          className="text-text-secondary hover:text-bg-primary"
        >
          {shortenText(coin.websiteUrl, 30)}
        </Link>
      </p>

      <p className="mt-4 mb-2 text-text-primary font-medium text-lg">
        Historical Data
      </p>
      <div className="p-2 border border-border-light rounded-lg">
        <p className="flex items-center justify-between mb-4">
          <span>All time high</span>
          <span className="text-text-primary">
            {isLoading ? (
              "Updating..."
            ) : (
              <>
                <i className="text-xs text-text-secondary">
                  {formatEpochDateTime(
                    allTimeHigh?.time || 0,
                    true,
                    true,
                    true,
                    true,
                    true,
                    false
                  )}
                </i>{" "}
                {formatPrice(allTimeHigh?.price)}
              </>
            )}
          </span>
        </p>
        <p className="flex items-center justify-between ">
          <span>All time low</span>
          <span className="text-text-primary">
            {isLoading ? (
              "Updating..."
            ) : (
              <>
                <i className="text-xs text-text-secondary">
                  {formatEpochDateTime(
                    allTimeLow?.time || 0,
                    true,
                    true,
                    true,
                    true,
                    true,
                    false
                  )}
                </i>{" "}
                {formatPrice(allTimeLow?.price)}
              </>
            )}
          </span>
        </p>
      </div>

      <p className="mt-4 mb-2 text-text-primary font-medium text-lg">
        About {coin.name}
      </p>
      <p>{coin.description}</p>
    </>
  );
};

export default CoinDetails;
