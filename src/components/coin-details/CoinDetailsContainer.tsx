"use client";

import React, { useMemo } from "react";
import CoinDetails from "./CoinDetails";
import Coin<PERSON>hart from "./CoinChart";
import { Coin } from "@/lib/api.interface";
import { TimePeriod } from "@/lib/types";
import { useState } from "react";
import { useCoinPriceHistory } from "@/lib/state";

const CoinDetailsContainer = ({ coin }: { coin: Coin }) => {
  const [timePeriod, setTimePeriod] = useState<TimePeriod>("24h");
  const cointPriceHistoryQuery = useCoinPriceHistory(coin.uuid, timePeriod);
  const coinPriceHistory = cointPriceHistoryQuery.data?.data.history;
  const coinPrices = useMemo(
    () =>
      coinPriceHistory
        ?.map((entry) => ({
          time: entry.timestamp.toString(),
          price: +entry.price,
        }))
        .reverse(),
    [coinPriceHistory]
  );

  const allTimeHigh = coinPrices?.reduce((prev, curr) => {
    return prev.price > curr.price ? prev : curr;
  }, coinPrices?.[0]);

  const allTimeLow = coinPrices?.reduce((prev, curr) => {
    return prev.price < curr.price ? prev : curr;
  }, coinPrices?.[coinPrices.length - 1]);

  return (
    <div className="flex md:border md:border-border-light md:rounded-lg">
      <div className="md:w-1/3 w-full  md:border-r md:border-border-light md:rounded-lg md:p-8">
        <CoinDetails
          coin={coin}
          allTimeHigh={{
            time: parseInt(allTimeHigh?.time || "0"),
            price: allTimeHigh?.price || 0,
          }}
          allTimeLow={{
            time: parseInt(allTimeLow?.time || "0"),
            price: allTimeLow?.price || 0,
          }}
          isLoading={cointPriceHistoryQuery.isLoading}
        />
      </div>
      <div className="md:w-2/3 w-full p-8 md:block hidden">
        <CoinChart coin={coin} timePeriodSetter={setTimePeriod} />
      </div>
    </div>
  );
};

export default CoinDetailsContainer;
