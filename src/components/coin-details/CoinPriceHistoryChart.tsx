"use client";

import {
  Line,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  Area,
  ResponsiveContainer,
  ComposedChart,
} from "recharts";
import {
  formatEpochDateTime,
  formatPrice,
  formatUnixToChartDate,
} from "../../lib/utils";

export type DataPoint = {
  time: string;
  price: number;
  volume?: number;
};

const COLORS = {
  BORDER: "#409E13",
  DARK: "#62A442",
  FILL: "#CAF2B6",
  WHITE: "#FFFFFF",
};

type ChartPropTypes = {
  data: DataPoint[];
  id: string;
  showTime?: boolean;
  showDate?: boolean;
};

const CoinPriceHistoryChart = ({
  data,
  id,
  showTime,
  showDate,
}: ChartPropTypes) => {
  return (
    <div style={{ width: "100%", height: 400 }}>
      <ResponsiveContainer width="100%" height={400} key={id}>
        <ComposedChart data={data}>
          <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
          <XAxis
            dataKey="time"
            tickFormatter={(value) =>
              formatUnixToChartDate(value, { showTime, showDate })
            }
            tick={{ fontSize: 10 }}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            orientation="right"
            domain={["dataMin", "dataMax"]}
            tickFormatter={(tick) => `$${tick / 1000}k`}
            tick={{ fontSize: 10 }}
            tickLine={false}
            axisLine={false}
          />
          <Tooltip
            formatter={(value) => [
              `$${formatPrice(parseFloat(value as string))}`,
              "Price",
            ]}
            labelFormatter={(label) =>
              `Time: ${formatEpochDateTime(label as number)}`
            }
          />
          <Area
            type="monotone"
            dataKey="price"
            stroke="none"
            fill={COLORS.FILL}
            tooltipType="none"
          />
          <Line
            type="monotone"
            dataKey="price"
            stroke={COLORS.BORDER}
            strokeWidth={2}
            dot={false}
            activeDot={{ r: 6, stroke: COLORS.WHITE, strokeWidth: 2 }}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

export default CoinPriceHistoryChart;
