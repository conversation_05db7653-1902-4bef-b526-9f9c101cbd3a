"use client";

import { Coin } from "@/lib/api.interface";
import { AssetValueChange } from "../home/<USER>";
import { useBookmarks } from "@/lib/state";
import Button from "../comman/Button";
import { useRouter } from "next/navigation";
import { convertToKebabCase, formatPrice, shortenText } from "@/lib/utils";
import { ConfigProvider, Table, TableProps } from "antd";
import { MouseEvent, useState } from "react";
import { Pagination } from "../comman/Pagination";

const columns: TableProps<Coin>["columns"] = [
  {
    title: "#",
    key: "index",
    dataIndex: "index",
    align: "center",
    width: 50,
    render: (_text: string, _coin, index) => (
      <span className="text-sm text-text-primary">{index + 1}</span>
    ),
  },
  {
    title: "Asset",
    dataIndex: "name",
    key: "name",
    align: "left",
    width: 200,
    render: (text: string, coin) => (
      <div className=" flex gap-2">
        <picture>
          <img height={24} width={24} src={coin.iconUrl} alt={coin.name} />
        </picture>
        <span className="hidden md:block text-base font-medium text-text-primary">
          {shortenText(text, 15)}{" "}
          <span className="text-sm text-text-primary">({coin.symbol})</span>
        </span>
      </div>
    ),
  },
  {
    title: "Prices",
    dataIndex: "price",
    key: "price",
    width: 120,
    align: "left",
    sorter: (a, b) => parseFloat(a.price) - parseFloat(b.price),
    render: (text: string) => (
      <span className="text-sm text-text-primary">
        ${formatPrice(parseFloat(text))}
      </span>
    ),
  },
  {
    title: "1h",
    dataIndex: "change_1h",
    key: "change_1h",
    align: "left",
    width: 100,
    sorter: (a, b) => parseFloat(a.change_1h) - parseFloat(b.change_1h),
    render: (text: string) => <AssetValueChange value={text} />,
  },
  {
    title: "24h",
    dataIndex: "change",
    key: "change_24h",
    align: "left",
    width: 100,
    sorter: (a, b) => parseFloat(a.change) - parseFloat(b.change),
    render: (text: string) => <AssetValueChange value={text} />,
  },
  {
    title: "24h Volume",
    dataIndex: "24hVolume",
    key: "24hVolume",
    align: "left",
    width: 100,
    sorter: (a, b) => parseFloat(a["24hVolume"]) - parseFloat(b["24hVolume"]),
    render: (text: string) => (
      <span className="text-sm text-text-primary">
        ${formatPrice(parseFloat(text))}
      </span>
    ),
  },
  {
    title: "Market Cap",
    dataIndex: "marketCap",
    key: "marketCap",
    align: "left",
    width: 150,
    sorter: (a, b) => parseFloat(a?.marketCap) - parseFloat(b?.marketCap),
    render: (text: string) => (
      <span className="text-sm text-text-primary">
        ${formatPrice(parseFloat(text))}
      </span>
    ),
  },
  {
    title: "",
    key: "actions01",
    width: 150,
    dataIndex: "isBookmarked",
    render: (_isBookmarked: boolean, coin: Coin) => <Bookmark coin={coin} />,
  },
];

const Bookmark = ({ coin }: { coin: Coin }) => {
  const { removeBookmark, removeBookmarkIsPending } = useBookmarks({});
  const isPending = removeBookmarkIsPending;

  const handleRemoveBookmark = (
    e: MouseEvent<HTMLImageElement, MouseEvent>
  ) => {
    e.preventDefault();
    e.stopPropagation();
    removeBookmark(coin.uuid);
  };

  return (
    <div>
      <Button
        variant="destructive"
        // @ts-expect-error onClick type mismatch
        onClick={handleRemoveBookmark}
        disabled={isPending}
        loading={isPending}
      >
        Remove
      </Button>
    </div>
  );
};

const CollectionTable = () => {
  const router = useRouter();
  const { userBookmarksData, userBookmarksLoader } = useBookmarks({});
  const [coinList] = userBookmarksData?.coins || [];
  const total = coinList?.length || 0;
  const [page, setPage] = useState(1);
  const pageSize = 20;
  const totalPages = Math.ceil(total / pageSize);

  const handlePagination = (page: number) => {
    setPage(page);
  };

  const coinData = coinList || [];

  const dataSource = coinData.slice((page - 1) * pageSize, page * pageSize);

  return (
    <div>
      <ConfigProvider
        theme={{
          components: {
            Spin: {
              colorPrimary: "#bbd955",
            },
            Table: {
              fontFamily: "DM Sans",
              colorPrimary: "#bbd955",
            },
          },
        }}
      >
        <Table
          columns={columns}
          dataSource={dataSource || []}
          pagination={false}
          loading={userBookmarksLoader}
          className="crypto-table"
          onRow={(record) => ({
            onClick: () => {
              router.push(`/coins/${convertToKebabCase(record.name)}`);
            },
          })}
          scroll={{ x: "600px" }}
        />
      </ConfigProvider>
      <div className="mt-4">
        <Pagination
          totalPages={totalPages}
          currentPage={page}
          onPageChange={handlePagination}
        />
      </div>
    </div>
  );
};

export default CollectionTable;
