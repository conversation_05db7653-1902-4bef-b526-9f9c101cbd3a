import {
  Button as BaseButton,
  ButtonProps as BaseButtonProps,
} from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";

// Extended button props interface
interface ButtonProps extends Omit<BaseButtonProps, "asChild"> {
  children: string | React.ReactNode;
  prefixIcon?: React.ReactNode;
  suffixIcon?: React.ReactNode;
  loading?: boolean;
  htmlFor?: string;
  onClick?: () => void;
}

const Button: React.FC<ButtonProps> = ({
  children = "Button",
  prefixIcon,
  suffixIcon,
  loading = false,
  disabled = false,
  htmlFor,
  type = "button",
  variant = "default",
  onClick,
  className = "",
  ...props
}) => {
  // Create the button element or a label wrapper if htmlFor is provided
  const ButtonElement = () => (
    <BaseButton
      type={type}
      variant={variant}
      disabled={disabled || loading}
      onClick={onClick}
      className={cn(
        "relative flex items-center justify-center gap-2 hover:cursor-pointer",
        className
      )}
      {...props}
    >
      {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
      {!loading && prefixIcon && <span className="mr-2">{prefixIcon}</span>}
      <span>{children}</span>
      {!loading && suffixIcon && <span className="ml-2">{suffixIcon}</span>}
    </BaseButton>
  );

  // If htmlFor is provided, wrap the button in a label
  if (htmlFor) {
    return (
      <label htmlFor={htmlFor}>
        <ButtonElement />
      </label>
    );
  }

  return <ButtonElement />;
};

export default Button;
