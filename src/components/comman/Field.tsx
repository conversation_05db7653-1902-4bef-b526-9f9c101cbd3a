import * as React from "react";
import { cn } from "@/lib/utils";
import { Info } from "lucide-react";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export interface FieldProps extends React.HTMLAttributes<HTMLDivElement> {
  control: React.ReactElement;
  description?: string;
  disabled?: boolean;
  error?: boolean | string;
  hint?: React.ReactNode;
  label: string;
  required?: boolean;
}

// Add type for control props
interface ControlProps extends React.HTMLAttributes<HTMLElement> {
  className?: string;
  disabled?: boolean;
  required?: boolean;
  "aria-invalid"?: boolean;
  "aria-errormessage"?: string;
  "aria-describedby"?: string;
  id?: string;
}

const Field = React.forwardRef<HTMLDivElement, FieldProps>(
  (
    {
      className,
      control,
      description,
      disabled = false,
      error = false,
      hint,
      label,
      required = false,
      ...props
    },
    ref
  ) => {
    const id = React.useId();
    const secondaryId = React.useId();
    const secondary = error === false ? description : error;

    const hintElement = hint ? (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="inline-flex shrink-0 text-muted-foreground">
              <Info className="h-4 w-4" />
            </span>
          </TooltipTrigger>
          <TooltipContent side="top">{hint}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    ) : null;

    // Create merged props for the control
    const controlProps: ControlProps = {
      id,
      disabled,
      required,
      "aria-invalid": error ? true : undefined,
      "aria-errormessage":
        secondary && error !== false ? secondaryId : undefined,
      "aria-describedby":
        secondary && error === false ? secondaryId : undefined,
      className: cn(
        "flex h-12 w-full rounded-md border border-input bg-background px-3 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
        error && "border-destructive focus-visible:ring-destructive",
        (control.props as ControlProps)?.className // Type assertion here
      ),
    };

    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col gap-2",
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        aria-disabled={disabled}
        aria-invalid={error ? true : undefined}
        {...props}
      >
        <Label
          htmlFor={id}
          className="flex items-center gap-1 text-sm font-medium leading-none"
        >
          {hintElement}
          <span>{label}</span>
          {required && (
            <span
              className="inline-block h-1 w-1 rounded-full bg-destructive"
              aria-label="Required"
            />
          )}
        </Label>

        {React.cloneElement(control, controlProps)}

        {secondary && (
          <p
            id={secondaryId}
            className={cn(
              "text-xs",
              error ? "text-destructive" : "text-muted-foreground"
            )}
          >
            {secondary}
          </p>
        )}
      </div>
    );
  }
);

Field.displayName = "Field";

export { Field };
