import { ArrowUpRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const Footer = () => {
  return (
    <section className="bg-bg-black text-white">
      <div className="center py-16">
        <div className="flex md:items-center justify-between mb-8 flex-col md:flex-row">
          <div>
            <Image alt="logo" width={140} height={40} src={"/MOFSE.svg"} />
          </div>
          <ul className="flex md:hidden gap-4 text-sm font-medium flex-col md:flex-row mt-4 md:mt-0">
            <li>
              <Link
                href="/"
                className="w-full flex items-center justify-between border-b border-white pb-4 hover:text-bg-primary"
              >
                <span>Home</span>
                <ArrowUpRight />
              </Link>
            </li>
            <li>
              <Link
                href="/about-us"
                className="w-full flex items-center justify-between border-b border-white pb-4 hover:text-bg-primary"
              >
                <span>About Us</span>
                <ArrowUpRight />
              </Link>
            </li>
            <li>
              <Link
                href="/learn"
                className="w-full flex items-center justify-between border-b border-white pb-4 hover:text-bg-primary"
              >
                <span>Learn</span>
                <ArrowUpRight />
              </Link>
            </li>
            <li>
              <Link
                href="/mofse-advance"
                className="w-full flex items-center justify-between border-b border-white pb-4 hover:text-bg-primary"
              >
                <span>Stablecoins</span>
                <ArrowUpRight />
              </Link>
            </li>
            <li>
              <Link
                href="/mofse-weekly"
                className="w-full flex items-center justify-between border-b border-white pb-4 hover:text-bg-primary"
              >
                <span>Regulatory Tracker</span>
                <ArrowUpRight />
              </Link>
            </li>
            <li>
              <Link
                href="/advertise-with-us"
                className="w-full flex items-center justify-between border-b border-white pb-4 hover:text-bg-primary"
              >
                <span>Advertise with us</span>
                <ArrowUpRight />
              </Link>
            </li>
            <li>
              <Link
                href="/terms-and-conditions"
                className="w-full flex items-center justify-between border-b border-white pb-4 hover:text-bg-primary"
              >
                <span>Terms & Conditions</span>
                <ArrowUpRight />
              </Link>
            </li>
            <li>
              <Link
                href="/privacy-policy"
                className="w-full flex items-center justify-between border-b border-white pb-4 hover:text-bg-primary"
              >
                <span>Privacy Policy</span>
                <ArrowUpRight />
              </Link>
            </li>
          </ul>
          <ul className="md:flex gap-4 text-sm font-medium flex-col md:flex-row mt-4 md:mt-0 hidden">
            <li>
              <Link
                href="/"
                className="hover:text-bg-primary transition-colors"
              >
                Home
              </Link>
            </li>
            <li>
              <Link
                href="/about-us"
                className="hover:text-bg-primary transition-colors"
              >
                About Us
              </Link>
            </li>
            <li>
              <Link
                href="/learn"
                className="hover:text-bg-primary transition-colors"
              >
                Learn
              </Link>
            </li>
            <li>
              <Link
                href="/mofse-advance"
                className="hover:text-bg-primary transition-colors"
              >
                Stablecoins
              </Link>
            </li>
            <li>
              <Link
                href="/mofse-weekly"
                className="hover:text-bg-primary transition-colors"
              >
                <span>Regulatory Tracker</span>
              </Link>
            </li>
          </ul>
        </div>

        <p className="text-justify mb-4 text-sm hidden md:block">
          Our platform serves as a trusted information companion, offering a comprehensive suite of tools and insights. We bridge established frameworks with innovative perspectives, combining on chain and off chain insights to focus on bigger picture. Committed to serving others, we prioritize accessibility and inclusivity for all. For Any queries{" "}
          <Link href="mailto:<EMAIL>" className="underline">
            <EMAIL>
          </Link>
        </p>

          <p className="text-[0.7rem] text-justify text-gray-400">We pledge to contribute 20% of our profits to causes that align with our values.</p>

        <hr className="mb-4 border-[0.5px] border-border-light hidden md:block" />

        <div className="mb-4  md:flex justify-between text-sm hidden ">
          <span>© 2025 MOFSE. All rights reserved.</span>
          <ul className="flex gap-4">
            <li>
              <Link
                href="/advertise-with-us"
                className="hover:text-bg-primary transition-colors"
              >
                Advertise with us
              </Link>
            </li>
            <li>
              <Link
                href="/terms-and-conditions"
                className="hover:text-bg-primary transition-colors"
              >
                Terms & Conditions
              </Link>
            </li>

            <li>
              <Link
                href="/privacy-policy"
                className="hover:text-bg-primary transition-colors"
              >
                Privacy Policy
              </Link>
            </li>
          </ul>
        </div>

        <hr className="mb-8 border-[0.5px] border-border-light hidden md:block" />

        <p className="text-justify mb-8 text-sm block md:hidden">
          Our platform serves as a trusted information companion, offering a comprehensive suite of tools and insights. We bridge established frameworks with innovative perspectives, combining on chain and off chain insights to focus on bigger picture. Committed to serving others, we prioritize accessibility and inclusivity for all. For Any queries{" "}
          <Link href="mailto:<EMAIL>" className="underline">
            <EMAIL>
          </Link>
        </p>

        <p className="text-justify text-sm">
          <span className="font-bold">RISK DISCLAIMER:</span>
          This websites content is intended solely for general informational
          purposes and should not be interpreted as trading, investment,
          financial, or licensed educational advice. The markets for
          cryptocurrencies are extremely risky and volatile. Due to the high
          level of financial risk involved, not everyone should invest in,
          trade, or work with digital assets. Before making any investment
          decisions, we strongly advise that you speak with a professional or
          registered financial advisor. In addition to not providing investment
          advisory services, MOFSE disclaims all liability for any losses
          brought on by reliance on the data on this platform. Regarding the
          correctness, comprehensiveness, timeliness, or dependability of the
          information on this website, MOFSE makes no guarantees or
          representations. We are not liable for any errors, omissions, delays,
          or losses that may result from reliance on the provided information. Information related to digital assets or any of our services is directed at persons outside the United Kingdom and must not be acted upon by persons in the United Kingdom.
        </p>
      </div>
    </section>
  );
};

export default Footer;
