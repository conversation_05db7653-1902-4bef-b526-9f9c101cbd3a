"use client";
import { useScreenSize } from "@/lib/hook";
import parse from "html-react-parser";
import { useEffect } from "react";

const HTMLContentParser = ({ content }: { content: string }) => {
  const { width } = useScreenSize();
  const isMobile = width! < 768;

  useEffect(() => {
    if (isMobile) {
      const videos = document.getElementsByTagName("iframe");

      for (let i = 0; i < videos.length; i++) {
        videos[i].setAttribute("style", "width: 100%;");
      }
    }
  }, [content, isMobile, width]);

  return (
    <section className="no_css_styling">
      <div>{parse(content || "")}</div>
    </section>
  );
};

export default HTMLContentParser;
