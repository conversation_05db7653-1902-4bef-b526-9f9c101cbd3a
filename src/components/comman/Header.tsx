"use client";
import { useProfile } from "@/lib/state";
import { cn, getInitials } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { Avatar, AvatarFallback } from "../ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useEffect, useState } from "react";
import { ChevronDown, ChevronUp, Menu, X } from "lucide-react";
import { signOut } from "firebase/auth";
import { auth } from "@/config/firebase";
import { toast } from "sonner";
import Button from "./Button";
import { STORAGE_KEYS } from "@/lib/constant";

const MenuItems = [
  {
    label: "Home",
    href: "/",
    icon: "/home.svg",
  },
  // {
  //   label: "Exchanges",
  //   href: "/exchanges",
  //   icon: "/about-us.svg",
  // },
  // {
  //   label: "Research",
  //   href: "/research",
  //   icon: "/projects.svg",
  // },
  // {
  //   label: "Learn",
  //   href: "/learn",
  //   icon: "/learn-crypto.svg",
  // },
  {
    label: "Regulatory Tracker",
    href: "/mofse-weekly",
    icon: "/projects.svg",
  },
  {
    label: "Cryptocurrency",
    href: "/alerts",
    icon: "/ecosystem.svg",
  },
  {
    label: "Stablecoins",
    href: "/mofse-advance",
    icon: "/ecosystem.svg",
  },
  {
    label: "CBDC",
    href: "/cbdc",
    icon: "/ecosystem.svg",
  },
  {
    label: "Enterprise Infrastructure Solutions",
    href: "/mofse-bridge",
    icon: "/ecosystem.svg",
  },
];

const loggedMenuItems = [
  {
    label: "Profile",
    href: "/profile",
    icon: "/profile.svg",
  },
  {
    label: "Collections",
    href: "/collections",
    icon: "/star-white.svg",
  },
  {
    label: "Logout",
    href: "/logout",
    icon: "/logout.svg",
  },
];

const Header = ({ isDarkTheme = false }: { isDarkTheme?: boolean }) => {
  const router = useRouter();
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showMobileMenu, setShowMobileMenu] = useState<boolean>(false);
  const pathname = usePathname();
  const userQuery = useProfile();
  const user = userQuery.data;

  const handleLogout = async () => {
    toast.promise(signOut(auth), {
      loading: "Loading...",
      success: () => {
        return "Logout successful";
      },
      error: "Error",
    });

    localStorage.clear();
    window.location.href = "/";
  };

  useEffect(() => {
    const loggedInTime = localStorage.getItem(STORAGE_KEYS.LOGGED_IN_TIME);
    const currentTime = new Date().getTime();
    const timeDifference = currentTime - parseInt(loggedInTime!);
    if (timeDifference > 60 * 60 * 8 * 1000) {
      void toast.warning("You have been logged out due to inactivity.");
      void handleLogout();
    }
  }, []);

  return (
    <>
      <header className={cn("sticky top-0 z-50 py-3", isDarkTheme ? "text-black" : "bg-black")} style={isDarkTheme ? { backgroundColor: '#222831' } : {}}>
        <section className="center flex items-center justify-between flex-row tablet:flex-col lg:flex-row relative">
          <Link href="/">
            <Image src="/MOFSE.svg" alt="logo" width={140} height={40} />
          </Link>
          <ul className="hidden tablet:flex gap-1 lg:gap-2 xl:gap-4">
            {MenuItems.map((item, index) => (
              <li key={index}>
                <Link
                  href={item.href}
                  className={cn(
                    "py-2 px-4 text-sm rounded-sm cursor-pointer hover:text-black",
                    isDarkTheme
                      ? "hover:bg-gray-700 border-transparent text-white"
                      : "hover:bg-bg-primary text-white",
                    item.href === "/mofse-bridge"
                      ? isDarkTheme
                        ? "border-2 border-bg-primary"
                        : "border-2 border-bg-primary"
                      : "",
                    item.href === pathname
                      ? isDarkTheme
                        ? "bg-gray-700 border-2 border-transparent"
                        : "bg-bg-primary text-black"
                      : ""
                  )}
                >
                  {item.label}
                </Link>
              </li>
            ))}
          </ul>
          <div className="hidden tablet:flex items-center gap-2 ">
            {user ? (
              <DropdownMenu open={showDropdown} onOpenChange={setShowDropdown}>
                <DropdownMenuTrigger className="flex items-center gap-2 outline-0">
                  <div className="flex items-center gap-2">
                    <Avatar className="w-12 h-12 ml-2">
                      <AvatarFallback className="bg-bg-primary text-text-primary">
                        {getInitials(user.username)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="lg:block hidden">
                      <p>{user.username}</p>
                      <p className="text-left">User</p>
                    </div>
                  </div>
                  {showDropdown ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </DropdownMenuTrigger>

                <DropdownMenuContent className="w-52">
                  <DropdownMenuItem onClick={() => router.push("/profile")}>
                    Profile
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => router.push("/collections")}>
                    Collections
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleLogout}>
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Link
                href={"/sign-up"}
                className={cn(
                  "py-2 px-4 rounded-sm cursor-pointer font-bold",
                  isDarkTheme
                    ? "bg-bg-primary text-text-primary"
                    : "bg-bg-primary  text-black"
                )}
              >
                Free Trial
              </Link>
            )}
          </div>
          <div className="tablet:hidden flex items-center gap-2">
            {!user && (
              <Link href={"/sign-up"}>
                <Button>Free Trial</Button>
              </Link>
            )}
            <button
              onClick={() => setShowMobileMenu(true)}
              className="cursor-pointer text-[#bbd955]"
            >
              <Menu />
            </button>
          </div>
          {showMobileMenu && (
            <div className="absolute top-[-12px] right-0 bg-bg-black h-screen w-full p-5">
              <div className="flex items-center justify-between">
                {user ? (
                  <div className="flex items-center gap-2">
                    <Avatar className="w-12 h-12 ml-2">
                      <AvatarFallback className="bg-bg-primary text-white">
                        {getInitials(user.username)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col text-white">
                      <p>{user.username}</p>
                      <p className="text-left">User</p>
                    </div>
                  </div>
                ) : (
                  <Link href="/">
                    <Image src="/MOFSE.svg" alt="logo" width={71} height={25} />
                  </Link>
                )}
                <X
                  className="h-8 w-8 text-white cursor-pointer"
                  onClick={() => setShowMobileMenu(false)}
                />
              </div>

              <ul
                className={cn("flex flex-col gap-8 mt-8", user ? "pl-2" : "")}
              >
                {MenuItems.map((item, index) => (
                  <li key={index} className="flex items-center gap-4">
                    <Image
                      src={item.icon}
                      alt={item.label}
                      width={24}
                      height={24}
                    />

                    <Link href={item.href} className="text-white">
                      {item.label}
                    </Link>
                  </li>
                ))}
                {user &&
                  loggedMenuItems.map((item, index) => (
                    <li key={index} className="flex items-center gap-4">
                      <Image
                        src={item.icon}
                        alt={item.label}
                        width={24}
                        height={24}
                        className="text-white"
                      />

                      {item.label === "Logout" ? (
                        <button onClick={handleLogout} className="text-white">
                          Logout
                        </button>
                      ) : (
                        <Link href={item.href} className="text-white">
                          {item.label}
                        </Link>
                      )}
                    </li>
                  ))}
              </ul>
            </div>
          )}
        </section>
      </header>
    </>
  );
};

export default Header;
