"use client";

import {
  Pagination as ShadcnPagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

interface PageSizeOption {
  label: string;
  value: string;
}

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
  position?: "left" | "center" | "right";
  pageSizeOptions?: PageSizeOption[];
  onPageSizeChange?: (value: string) => void;
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  className,
  pageSizeOptions,
  onPageSizeChange,
}: PaginationProps) {
  return (
    <ShadcnPagination
      className={cn(
        "w-full",
        pageSizeOptions ? "justify-between" : "justify-center",
        "flex-col md:flex-row gap-2",
        className
      )}
    >
      {pageSizeOptions && <div className="w-full md:w-1/3"></div>}
      <PaginationContent
        className={cn(pageSizeOptions ? "w-full md:w-1/3" : "", " m-auto")}
      >
        <PaginationItem>
          <PaginationPrevious
            onClick={() => onPageChange(Math.max(1, currentPage - 1))}
            className={currentPage <= 1 ? "pointer-events-none opacity-50" : ""}
          />
        </PaginationItem>

        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
          let pageNum: number;

          if (totalPages <= 5) {
            pageNum = i + 1;
          } else {
            if (currentPage <= 3) {
              pageNum = i + 1;
              if (i === 4) {
                return (
                  <PaginationItem key="ellipsis-end">
                    <PaginationEllipsis />
                  </PaginationItem>
                );
              }
            } else if (currentPage >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
              if (i === 0) {
                return (
                  <PaginationItem key="ellipsis-start">
                    <PaginationEllipsis />
                  </PaginationItem>
                );
              }
            } else {
              if (i === 0) {
                return (
                  <PaginationItem key="ellipsis-start">
                    <PaginationEllipsis />
                  </PaginationItem>
                );
              }
              if (i === 4) {
                return (
                  <PaginationItem key="ellipsis-end">
                    <PaginationEllipsis />
                  </PaginationItem>
                );
              }
              pageNum = currentPage + i - 2;
            }
          }

          return (
            <PaginationItem key={pageNum}>
              <PaginationLink
                isActive={pageNum === currentPage}
                onClick={() => onPageChange(pageNum)}
                className={cn(
                  pageNum === currentPage && "bg-bg-primary text-text-primary"
                )}
              >
                {pageNum}
              </PaginationLink>
            </PaginationItem>
          );
        })}

        <PaginationItem>
          <PaginationNext
            onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
            className={
              currentPage >= totalPages ? "pointer-events-none opacity-50" : ""
            }
          />
        </PaginationItem>
      </PaginationContent>
      {/* create page size selector */}
      {pageSizeOptions && (
        <div className="w-full md:w-1/3 flex justify-start md:justify-end">
          <div className="flex rounded-md bg-white border border-gray-200 shadow-sm max-w-xs items-center w-fit">
            <div className="flex items-center justify-end px-4 py-2 bg-gray-50 text-gray-700 font-medium border-r border-gray-200 rounded-l-md">
              Rows :
            </div>
            <Select
              defaultValue={pageSizeOptions[0].value}
              onValueChange={onPageSizeChange}
            >
              <SelectTrigger className="border-none shadow-none focus:ring-0 rounded-l-none">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {pageSizeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
    </ShadcnPagination>
  );
}
