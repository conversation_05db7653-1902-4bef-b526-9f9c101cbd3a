import { Search } from "lucide-react";
import React from "react";

interface SearchBarProps {
  defaultValue?: string;
  handleSearch: (search: string) => void;
  placeholder?: string;
}

const SearchBar = ({
  defaultValue,
  handleSearch,
  placeholder = "Search topics...", // Default placeholder
}: SearchBarProps) => {
  return (
    <div className="flex px-2 py-1 gap-2 items-center rounded w-full md:w-96 justify-between border border-border-light">
      <input
        type="search"
        placeholder={placeholder}
        onChange={(e) => handleSearch(e.target.value)}
        defaultValue={defaultValue || ""}
        className="w-full hover:outline-0 focus:outline-0 text-sm md:text-base"
      />
      <span className="px-2 py-1.5 bg-bg-black rounded">
        <Search className="text-white" />
      </span>
    </div>
  );
};

export default SearchBar;
