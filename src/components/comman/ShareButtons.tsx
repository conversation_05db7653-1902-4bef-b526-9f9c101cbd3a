"use client";
import { PUBLIC_SITE_URL } from "@/lib/constant";
import { Link } from "lucide-react";
import React from "react";
import {
  FacebookShareButton,
  LinkedinShareButton,
  TwitterShareButton,
  FacebookIcon,
  LinkedinIcon,
  XIcon,
} from "react-share";
import { toast } from "sonner";

const ShareButtons = ({
  slug,
  title,
  path,
}: {
  slug: string;
  title: string;
  path: string;
}) => {
  const copyToClipboard = () => {
    void navigator.clipboard.writeText(`${PUBLIC_SITE_URL}/${path}/${slug}`);
    void toast.success("Link copied to clipboard");
  };

  return (
    <div className="flex gap-4">
      <LinkedinShareButton
        url={`${PUBLIC_SITE_URL}/${path}/${slug}`}
        title={title}
      >
        <LinkedinIcon size={48} round />
      </LinkedinShareButton>
      <FacebookShareButton
        url={`${PUBLIC_SITE_URL}/${path}/${slug}`}
        title={title}
      >
        <FacebookIcon size={48} round />
      </FacebookShareButton>
      <TwitterShareButton
        url={`${PUBLIC_SITE_URL}/${path}/${slug}`}
        title={title}
      >
        <XIcon size={48} round />
      </TwitterShareButton>
      <button
        className="bg-gray-100 h-12 w-12 rounded-full flex items-center justify-center cursor-pointer"
        onClick={copyToClipboard}
      >
        <Link size={28} />
      </button>
    </div>
  );
};

export default ShareButtons;
