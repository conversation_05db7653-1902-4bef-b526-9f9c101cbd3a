"use client";
import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ChevronDown, ChevronUp, ChevronsUpDown } from "lucide-react";
import { Pagination } from "./Pagination";

// Type definitions
type SortOrder = "ascend" | "descend" | null;
type SizeType = "small" | "middle" | "large";
type AlignType = "left" | "center" | "right";
type RowScope = "row" | "rowgroup";

interface SorterResult<RecordType> {
  column: ColumnType<RecordType>;
  order: SortOrder;
  field: string;
}

interface PaginationConfig {
  current?: number;
  pageSize?: number;
  position?: "left" | "center" | "right";
}

export interface TableChangeInfo<RecordType> {
  pagination: PaginationConfig;
  filters: Record<string, string[]>;
  sorter: SorterResult<RecordType> | Record<string, SortOrder>;
  action: "paginate" | "sort" | "filter";
}

interface ScrollConfig {
  x?: number | string;
  y?: number | string;
}

interface ColumnType<RecordType> {
  key?: string;
  title:
    | React.ReactNode
    | ((data: {
        sortOrder?: SortOrder;
        filters?: Record<string, string[]>;
      }) => React.ReactNode);
  dataIndex: string | string[];
  align?: AlignType;
  width?: number | string;
  minWidth?: number;
  className?: string;
  ellipsis?: boolean | { showTitle?: boolean };
  sorter?: boolean | ((a: RecordType, b: RecordType) => number);
  sortDirections?: SortOrder[];
  sortOrder?: SortOrder;
  render?: (value: any, record: RecordType, index: number) => React.ReactNode;
  rowScope?: RowScope;
  onCell?: (
    record: RecordType,
    rowIndex: number
  ) => React.HTMLAttributes<HTMLTableCellElement>;
  hidden?: boolean;
}
type HeaderRowProps = React.ThHTMLAttributes<HTMLTableHeaderCellElement>;

interface TableProps<RecordType extends object = any> {
  columns?: ColumnType<RecordType>[];
  dataSource?: RecordType[];
  rowKey?: string | ((record: RecordType) => string);
  pagination?: PaginationConfig | false;
  bordered?: boolean;
  size?: SizeType;
  loading?: boolean;
  scroll?: ScrollConfig;
  rowHoverable?: boolean;
  onChange?: (info: TableChangeInfo<RecordType>) => void;
  onRow?: (
    record: RecordType,
    index: number
  ) => React.HTMLAttributes<HTMLTableRowElement>;
  onHeaderRow?: (
    column: ColumnType<RecordType>,
    index: number
  ) => HeaderRowProps;
  localSorting?: boolean;
}

// Demo data type
interface DemoDataType {
  key: string;
  name: string;
  age: number;
  address: string;
  tags: string[];
}

// Demo data
const demoData: DemoDataType[] = [
  {
    key: "1",
    name: "John Brown",
    age: 32,
    address: "New York No. 1 Lake Park",
    tags: ["nice", "developer"],
  },
  {
    key: "2",
    name: "Jim Green",
    age: 42,
    address: "London No. 1 Lake Park",
    tags: ["loser"],
  },
  {
    key: "3",
    name: "Joe Black",
    age: 32,
    address: "Sydney No. 1 Lake Park",
    tags: ["cool", "teacher"],
  },
];

const DataTable = <RecordType extends object = any>({
  columns = [],
  dataSource = demoData as unknown as RecordType[],
  rowKey = "key",
  pagination = { pageSize: 10, current: 1 },
  bordered = false,
  size = "middle",
  loading = false,
  scroll = {},
  rowHoverable = true,
  onChange = () => {},
  onRow = () => ({}),
  onHeaderRow = () => ({} as HeaderRowProps),
  localSorting = false,
}: TableProps<RecordType>) => {
  // State for sorting, filtering, and pagination
  const [sortState, setSortState] = useState<Record<string, SortOrder>>({});
  const [filters] = useState<Record<string, string[]>>({});
  const [currentPagination, setCurrentPagination] = useState<PaginationConfig>({
    current: pagination ? pagination.current || 1 : 1,
    pageSize: pagination ? pagination.pageSize || 10 : 10,
  });

  // Handle sort
  const handleSort = (column: ColumnType<RecordType>) => {
    if (!column.sorter) return;

    const key = Array.isArray(column.dataIndex)
      ? column.dataIndex.join(".")
      : column.dataIndex;
    const currentOrder = sortState[key];
    let newOrder: SortOrder = null;

    // Cycle through sort orders
    if (!currentOrder) {
      newOrder = "ascend";
    } else if (currentOrder === "ascend") {
      newOrder = "descend";
    }

    const newSortState = { ...sortState, [key]: newOrder };
    setSortState(newSortState);

    // Call onChange callback
    onChange({
      pagination: currentPagination,
      filters,
      sorter: { column, order: newOrder, field: key },
      action: "sort",
    });
  };

  // Handle pagination change
  const handlePageChange = (page: number) => {
    const newPagination = { ...currentPagination, current: page };
    setCurrentPagination(newPagination);

    onChange({
      pagination: newPagination,
      filters,
      sorter: sortState,
      action: "paginate",
    });
  };

  // Apply sort to data
  const getSortedData = (): RecordType[] => {
    const sortedData = [...dataSource];

    if (localSorting) {
      // Apply sorters
      Object.keys(sortState).forEach((key) => {
        const order = sortState[key];
        if (!order) return;

        const column = columns.find((col) => {
          const colKey = Array.isArray(col.dataIndex)
            ? col.dataIndex.join(".")
            : col.dataIndex;
          return colKey === key;
        });

        if (!column || !column.sorter) return;

        sortedData.sort((a, b) => {
          const result =
            typeof column.sorter === "function"
              ? column.sorter(a, b)
              : getValue(a, column.dataIndex) > getValue(b, column.dataIndex)
              ? 1
              : -1;

          return order === "ascend" ? result : -result;
        });
      });
    }

    return sortedData;
  };

  // Get current page data
  const getCurrentPageData = (): RecordType[] => {
    const sortedData = getSortedData();

    if (pagination === false) return sortedData;

    const current = currentPagination.current || 1;
    const pageSize = currentPagination.pageSize || 10;
    const start = (current - 1) * pageSize;
    const end = start + pageSize;

    return sortedData.slice(start, end);
  };

  // Render sort icons
  const renderSortIcon = (column: ColumnType<RecordType>) => {
    if (!column.sorter) return null;

    const key = Array.isArray(column.dataIndex)
      ? column.dataIndex.join(".")
      : column.dataIndex;
    const order = sortState[key];

    if (!order) {
      return <ChevronsUpDown className="ml-2 h-4 w-4 text-white" />;
    }

    return order === "ascend" ? (
      <ChevronUp className="ml-2 h-4 w-4 text-white" />
    ) : (
      <ChevronDown className="ml-2 h-4 w-4 text-white" />
    );
  };

  // Get value from record using dataIndex (supports nested paths)
  const getValue = (record: RecordType, dataIndex: string | string[]): any => {
    if (typeof dataIndex === "string") {
      return (record as any)[dataIndex];
    }

    if (Array.isArray(dataIndex)) {
      let value: any = record;
      for (const key of dataIndex) {
        value = value?.[key];
        if (value === undefined) break;
      }
      return value;
    }

    return undefined;
  };

  // Calculate table size styles
  const getSizeStyles = (): string => {
    switch (size) {
      case "small":
        return "text-sm py-1 px-2";
      case "large":
        return "text-base py-4 px-6";
      default: // middle
        return "text-sm py-3 px-4";
    }
  };

  const cellSizeStyles = getSizeStyles();

  // Calculate border styles
  const borderStyles = bordered
    ? "border border-border-light"
    : "border-b border-border-light";

  // Calculate hover styles
  const hoverStyles = rowHoverable
    ? "hover:bg-gray-50 transition-colors duration-150"
    : "";

  const currentData = getCurrentPageData();
  const total = dataSource.length;
  const pageSize = currentPagination.pageSize || 10;
  const totalPages = Math.ceil(total / pageSize);

  // Helper function to create a div that can be clicked to handle sort
  const createSortableHeader = (column: ColumnType<RecordType>) => {
    return (
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={() => column.sorter && handleSort(column)}
      >
        <span>
          {typeof column.title === "function"
            ? column.title({
                sortOrder:
                  sortState[
                    Array.isArray(column.dataIndex)
                      ? column.dataIndex.join(".")
                      : column.dataIndex
                  ],
                // Fix for the filters type
                filters: filters,
              })
            : column.title}
        </span>
        {renderSortIcon(column)}
      </div>
    );
  };

  return (
    <div className="w-full overflow-hidden rounded-md">
      {/* Table */}
      <div
        className={`${scroll.x ? "overflow-x-auto" : ""} ${
          scroll.y ? `max-h-[${scroll.y}px] overflow-y-auto` : ""
        }`}
      >
        <Table
          className={`w-full border-collapse ${
            bordered ? "border border-gray-200" : ""
          }`}
        >
          <TableHeader>
            <TableRow className={borderStyles}>
              {columns.map((column, index) => {
                if (column.hidden) return null;

                const headerCellProps: HeaderRowProps = onHeaderRow(
                  column,
                  index
                );

                const align = column.align || "left";
                const alignmentClass =
                  align === "center"
                    ? "text-center"
                    : align === "right"
                    ? "text-right"
                    : "text-left";

                const width = column.width ? `w-[${column.width}px]` : "";
                const minWidth = column.minWidth
                  ? `min-w-[${column.minWidth}px]`
                  : "";

                return (
                  <TableHead
                    key={
                      column.key ||
                      (typeof column.dataIndex === "string"
                        ? column.dataIndex
                        : column.dataIndex.join("."))
                    }
                    className={`${borderStyles} ${alignmentClass} font-semibold ${cellSizeStyles} ${width} ${minWidth} bg-bg-black text-white`}
                    {...headerCellProps}
                  >
                    {/* Use the helper function here instead of having onClick on TableHead */}
                    {createSortableHeader(column)}
                  </TableHead>
                );
              })}
            </TableRow>
          </TableHeader>
          <TableBody className="border border-border-light">
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.filter((col) => !col.hidden).length}
                  className="text-center py-8"
                >
                  <div className="flex items-center justify-center">
                    <div className="w-6 h-6 border-2 border-bg-primary border-t-transparent rounded-full animate-spin"></div>
                    <span className="ml-2">Loading...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : currentData.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={columns.filter((col) => !col.hidden).length}
                  className="text-center py-8"
                >
                  No data
                </TableCell>
              </TableRow>
            ) : (
              currentData.map((record, rowIndex) => {
                const rowProps = onRow(record, rowIndex);
                const rowId =
                  typeof rowKey === "function"
                    ? rowKey(record)
                    : (record as any)[rowKey];

                return (
                  <TableRow
                    key={rowId}
                    className={`${borderStyles} ${hoverStyles}`}
                    {...rowProps}
                  >
                    {columns.map((column, colIndex) => {
                      if (column.hidden) return null;

                      const cellProps = column.onCell
                        ? column.onCell(record, rowIndex)
                        : {};
                      const value = getValue(record, column.dataIndex);
                      const align = column.align || "left";
                      const alignmentClass =
                        align === "center"
                          ? "text-center"
                          : align === "right"
                          ? "text-right"
                          : "text-left";

                      const width = column.width ? `w-[${column.width}px]` : "";
                      const minWidth = column.minWidth
                        ? `min-w-[${column.minWidth}px]`
                        : "";
                      const ellipsis = column.ellipsis ? "truncate" : "";

                      return (
                        <TableCell
                          key={`${rowId}-${
                            column.key ||
                            (typeof column.dataIndex === "string"
                              ? column.dataIndex
                              : column.dataIndex.join(".")) ||
                            colIndex
                          }`}
                          className={`${borderStyles} ${alignmentClass} ${cellSizeStyles} ${width} ${minWidth} ${ellipsis}`}
                          scope={column.rowScope}
                          {...cellProps}
                        >
                          {column.render
                            ? column.render(value, record, rowIndex)
                            : value}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination !== false && (
        <Pagination
          currentPage={currentPagination.current || 1}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          className="mt-4"
        />
      )}
    </div>
  );
};

export type { ColumnType };

export default DataTable;
