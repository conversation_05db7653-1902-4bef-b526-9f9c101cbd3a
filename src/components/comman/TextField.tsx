import * as React from "react";
import { cn } from "@/lib/utils";
import { CircleAlert, Eye, EyeOff } from "lucide-react";

export interface TextFieldProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  end?: React.ReactNode | false;
  start?: React.ReactNode;
}

type TextFieldComponent = React.ForwardRefExoticComponent<
  TextFieldProps & React.RefAttributes<HTMLInputElement>
> & {
  Password: React.ForwardRefExoticComponent<
    Omit<TextFieldProps, "type"> & React.RefAttributes<HTMLInputElement>
  >;
};

const PasswordField = React.forwardRef<
  HTMLInputElement,
  Omit<TextFieldProps, "type">
>(
  (
    {
      className,
      disabled,
      end,
      "aria-invalid": invalid = false,
      start,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = React.useState(false);

    const togglePassword = () => setShowPassword((prev) => !prev);

    const endElement =
      end ??
      (invalid ? (
        <CircleAlert className="h-4 w-4 text-destructive" />
      ) : (
        <button
          type="button"
          onClick={togglePassword}
          className="flex items-center justify-center hover:text-foreground cursor-pointer "
        >
          {showPassword ? (
            <EyeOff className="h-4 w-4" />
          ) : (
            <Eye className="h-4 w-4" />
          )}
          <span className="sr-only">
            {showPassword ? "Hide password" : "Show password"}
          </span>
        </button>
      ));

    return (
      <TextField
        ref={ref}
        type={showPassword ? "text" : "password"}
        className={className}
        disabled={disabled}
        aria-invalid={invalid}
        start={start}
        end={endElement}
        {...props}
      />
    );
  }
);
PasswordField.displayName = "TextField.Password";

const TextField = React.forwardRef<HTMLInputElement, TextFieldProps>(
  (
    {
      className,
      disabled,
      end,
      "aria-invalid": invalid = false,
      start,
      ...props
    },
    ref
  ) => {
    const endElement =
      end ??
      (invalid ? <CircleAlert className="h-4 w-4 text-destructive" /> : null);

    return (
      <div
        className={cn(
          "flex items-center gap-2 w-full rounded-md border border-input bg-background px-3 shadow-sm transition-colors",
          "focus-within:ring-1 focus-within:ring-ring focus-within:border-ring",
          "hover:border-ring",
          invalid && "border-destructive focus-within:ring-destructive",
          disabled && "cursor-not-allowed opacity-50 bg-muted border-input",
          className
        )}
        aria-disabled={disabled}
        aria-invalid={invalid}
      >
        {start && (
          <div className="flex-shrink-0 text-muted-foreground">{start}</div>
        )}

        <input
          className={cn(
            "flex h-12 w-full bg-transparent text-sm placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed",
            "min-w-0 flex-1"
          )}
          disabled={disabled}
          ref={ref}
          {...props}
        />

        {endElement && (
          <div className="flex-shrink-0 text-muted-foreground">
            {endElement}
          </div>
        )}
      </div>
    );
  }
) as TextFieldComponent;

TextField.displayName = "TextField";
TextField.Password = PasswordField;

export { TextField };
