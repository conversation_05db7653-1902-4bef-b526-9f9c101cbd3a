"use client";

import { analytics } from "@/config/firebase";
import { logEvent, setAnalyticsCollectionEnabled } from "firebase/analytics";
import <PERSON><PERSON><PERSON> from "next/script";
import { useEffect } from "react";

// Extend Window interface to include cookieconsent
declare global {
  interface Window {
    cookieconsent?: {
      run: (config: any) => void;
    };
    dataLayer?: any[];
    gtag?: (...args: any[]) => void;
  }
}

export function getCookie(name: string): string | null {
  const cookies = document.cookie.split("; ");
  for (const cookie of cookies) {
    const [cookieName, cookieValue] = cookie.split("=");
    if (cookieName === name) {
      return decodeURIComponent(cookieValue);
    }
  }
  return null;
}

const CookieConsent = () => {
  useEffect(() => {
    // Initialize cookie consent after the script has loaded
    const initCookieConsent = () => {
      if (window.cookieconsent) {
        window.cookieconsent.run({
          notice_banner_type: "normal",
          consent_type: "express",
          palette: "light",
          language: "en",
          page_load_consent_levels: ["strictly-necessary"],
          notice_banner_reject_button_hide: false,
          preferences_center_close_button_hide: false,
          page_refresh_confirmation_buttons: false,
          website_name: "mofse.com",
          website_privacy_policy_url: "https://www.mofse.com/privacy-policy/",
        });
      }
    };

    // Check if the script is already loaded
    if (window.cookieconsent) {
      initCookieConsent();
    } else {
      // If not loaded, wait for it to load
      window.addEventListener("cookieconsentload", initCookieConsent);
    }

    return () => {
      window.removeEventListener("cookieconsentload", initCookieConsent);
    };
  }, []);

  useEffect(() => {
    // Function to handle clicks on the consent buttons

    const checkExistingConsent = () => {
      const cookieConsent = JSON.parse(
        getCookie("cookie_consent_level") || "{}"
      )?.tracking;

      if (cookieConsent) {
        setAnalyticsCollectionEnabled(analytics!, true);
        logEvent(analytics!, "test_event", {
          timestamp: new Date().toISOString(),
          test_param: "verification_test",
        });
      } else {
        setAnalyticsCollectionEnabled(analytics!, false);
      }
    };

    checkExistingConsent();

    const handleConsentButtonClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // Check if the clicked element or its parent has one of the classes
      const clickedElement = target.closest(".cc-nb-okagree, .cc-nb-reject");

      if (clickedElement) {
        if (clickedElement.classList.contains("cc-nb-okagree")) {
          setAnalyticsCollectionEnabled(analytics!, true);
          logEvent(analytics!, "test_event", {
            timestamp: new Date().toISOString(),
            test_param: "verification_test",
          });
        } else if (clickedElement.classList.contains("cc-nb-reject")) {
          setAnalyticsCollectionEnabled(analytics!, false);
        }
      }
    };

    // Add the global click event listener
    document.addEventListener("click", handleConsentButtonClick);

    // Clean up the event listener when component unmounts
    return () => {
      document.removeEventListener("click", handleConsentButtonClick);
    };
  }, []);

  return (
    <>
      <Script
        id="cookie-consent-script"
        src="//www.freeprivacypolicy.com/public/cookie-consent/4.2.0/cookie-consent.js"
        strategy="afterInteractive"
        onLoad={() => {
          window.dispatchEvent(new Event("cookieconsentload"));
        }}
      />

      {/* Google Analytics Script */}
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        data-cookie-consent="tracking"
        src="https://www.googletagmanager.com/gtag/js?id=G-RKSSKMH225"
      />
      <Script
        id="google-analytics-config"
        strategy="afterInteractive"
        data-cookie-consent="tracking"
      >
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'G-RKSSKMH225');
        `}
      </Script>
    </>
  );
};

export default CookieConsent;
