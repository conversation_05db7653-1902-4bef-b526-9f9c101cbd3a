"use client";
import { Exchange } from "@/lib/api.interface";
import { useExchanges } from "@/lib/state";
import { useState } from "react";
import Button from "../comman/Button";
import { ConfigProvider, Table, TableProps } from "antd";
import { Pagination } from "../comman/Pagination";

const columns: TableProps<Exchange>["columns"] = [
  {
    title: "Name",
    dataIndex: "name",
    key: "name",
    align: "left",
    render: (text: string, exc) => (
      <div className="flex gap-2">
        <picture>
          <img height={24} width={24} src={exc.iconUrl} alt={exc.name} />
        </picture>
        <span className="text-base font-medium text-text-primary">{text}</span>
      </div>
    ),
  },
  {
    title: "Number of Makerts",
    dataIndex: "numberOfMarkets",
    key: "numberOfMarkets",
    align: "left",
    sorter: (a, b) => {
      if (!a?.numberOfMarkets || !b?.numberOfMarkets) return 0;
      return a.numberOfMarkets - b.numberOfMarkets;
    },
    render: (text: string) => (
      <span className="text-sm text-text-primary">
        ${parseFloat(text).toLocaleString()}
      </span>
    ),
  },
  {
    title: "Number of Coins",
    dataIndex: "numberOfCoins",
    key: "numberOfCoins",
    align: "left",
    sorter: (a, b) => {
      if (!a?.numberOfCoins || !b?.numberOfCoins) return 0;
      return a.numberOfCoins - b.numberOfCoins;
    },
    render: (text: string) => (
      <span className="text-sm text-text-primary">
        {parseFloat(text).toLocaleString()}
      </span>
    ),
  },
  {
    title: "Market Share",
    dataIndex: "marketShare",
    key: "marketShare",
    align: "left",
    sorter: (a, b) => {
      if (!a?.marketShare || !b?.marketShare) return 0;
      return parseFloat(a.marketShare) - parseFloat(b.marketShare);
    },
    render: (text: string) => (
      <span className="text-sm text-text-primary">
        {parseFloat(text).toLocaleString()}%
      </span>
    ),
  },
  {
    title: "24h Volume",
    dataIndex: "24hVolume",
    key: "24hVolume",
    align: "left",
    sorter: (a, b) => {
      if (!a?.marketShare || !b?.marketShare) return 0;
      return parseFloat(a.marketShare) - parseFloat(b.marketShare);
    },
    render: (text: string) => (
      <span className="text-sm text-text-primary">
        {parseFloat(text).toLocaleString()}
      </span>
    ),
  },
];

const exchangeOptions = [
  {
    label: "All",
    value: "all",
  },
  {
    label: "Centralised",
    value: "centralised",
  },
  {
    label: "Decentralised",
    value: "decentralised",
  },
];

const ExchangesTable = () => {
  const [activeTab, setActiveTab] = useState("all");
  const exchangeQuery = useExchanges(activeTab);
  const exchangeData = exchangeQuery.data?.data.exchanges;
  const total = exchangeData?.length || 0;
  const [page, setPage] = useState(1);
  const pageSize = 20;
  const totalPages = Math.ceil(total / pageSize);

  const handlePagination = (page: number) => {
    setPage(page);
  };

  const dataSource = exchangeData?.slice(
    (page - 1) * pageSize,
    page * pageSize
  );

  const handleActiveTab = (tab: string) => {
    setActiveTab(tab);
    setPage(1);
  };

  return (
    <section className="mt-8">
      <div className="flex gap-4 mb-4">
        {exchangeOptions.map((option) => (
          <Button
            key={option.value}
            onClick={() => handleActiveTab(option.value)}
            variant={activeTab === option.value ? "default" : "outline"}
            className="text-text-primary"
          >
            {option.label}
          </Button>
        ))}
      </div>

      <ConfigProvider
        theme={{
          components: {
            Spin: {
              colorPrimary: "#bbd955",
            },
            Table: {
              fontFamily: "DM Sans",
              colorPrimary: "#bbd955",
            },
          },
        }}
      >
        <Table
          columns={columns}
          dataSource={dataSource || []}
          pagination={false}
          loading={exchangeQuery.isLoading}
          scroll={{ x: "600px" }}
          className="crypto-table"
        />
      </ConfigProvider>
      <div className="mt-4">
        <Pagination
          totalPages={totalPages}
          currentPage={page}
          onPageChange={handlePagination}
        />
      </div>
    </section>
  );
};

export default ExchangesTable;
