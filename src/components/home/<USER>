import { Coin } from "@/lib/api.interface";
import React from "react";
import { AssetValueChange } from "./AssetTable";
import Marquee from "react-fast-marquee";
import { formatPrice } from "@/lib/utils";

const AssetMarque = ({ allCoins }: { allCoins: Coin[] }) => {
  return (
    <section className="py-2 bg-[#1d1d1d]">
      <Marquee speed={50} gradient={false} autoFill={true}>
        {allCoins.map((coin) => (
          <div key={coin.uuid} className="flex items-center gap-4 mr-4">
            <picture>
              <img src={coin.iconUrl} alt={coin.name} className="w-6 h-6" />
            </picture>
            <div>
              <p className="text-sm text-gray-50">{coin.name}</p>
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium text-gray-50">
                  ${formatPrice(parseFloat(coin.price))}
                </span>
                <AssetValueChange value={coin.change_1h} />
              </div>
            </div>
          </div>
        ))}
      </Marquee>
    </section>
  );
};

export default AssetMarque;
