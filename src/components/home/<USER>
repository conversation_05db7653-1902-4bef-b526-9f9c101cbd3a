'use client'
import { useTypewriter, Cursor } from 'react-simple-typewriter';

export function HeroSectionWidget() {
  const [text] = useTypewriter({
    words: ['Stable Coins', 'Central Bank Digital Currency (CBDC)', 'Cryptocurrencies'],
    loop: true,
    typeSpeed: 120,
    deleteSpeed: 80,
    delaySpeed: 2000,
  });

  return (
    <div className="text-center p-8 md:p-12 rounded-lg max-w-4xl mx-auto">
      {/* Main heading */}
      <h1 className="text-4xl md:text-5xl font-extrabold text-white tracking-tight leading-tight">
        {/* <span>The Future of Digital Finance:</span> */}
        {/* <br /> */}
        <span className="block mt-4">
          Explore&nbsp;
          {/* <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#bbd955] via-[#bbd955] to-[#bbd955]"> */}
           <span className="bg-clip-text text-[#bbd955]">
            {/* The animated text will appear here */}
            {text}
          </span>
          <span className="text-[#bbd955]">
            <Cursor cursorStyle='_' />
          </span>
        </span>
      </h1>
    </div>
  );
}