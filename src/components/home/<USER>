"use client";
import { CB<PERSON><PERSON>urrency, Coin } from "@/lib/api.interface";
import { Pagination } from "../comman/Pagination";
import { useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { Triangle } from "lucide-react";
// Make sure to export and import your new hook and type
import { useBookmarks, useCbdc, useCoins, useUserLoggedIn } from "@/lib/state";
import SearchBar from "../comman/SearchBar";
import { useDebounce, useScreenSize } from "@/lib/hook";
import Loader from "../comman/Loader";
import { convertToKebabCase, formatPrice, shortenText } from "@/lib/utils";
import Button from "../comman/Button";
import Link from "next/link";
import { ConfigProvider, Table, TableProps } from "antd";
import { Bar, BarChart, Cell, ResponsiveContainer } from "recharts";


// Helper components (AssetValueChange, Bookmark) remain the same
export function AssetValueChange({ value }: { value: string }) {
  const isNegative = value?.includes("-");
  const updatedValue = isNegative ? value.replace("-", "") : value;

  return (
    <span
      className={`flex items-center text-sm gap-2 ${
        isNegative ? "text-red-500" : "text-green-600"
      }`}
    >
      <Triangle
        className={`w-3 h-3 ${isNegative ? "rotate-180" : "rotate-0"}`}
        fill={isNegative ? "red" : "green"}
      />
      {updatedValue}%
    </span>
  );
}

const UpdateRateChart = ({ rates }: { rates: number[] }) => {
  // Convert the raw numbers into a format recharts can use.
  const chartData = rates.map((value, index) => ({ name: `rate-${index}`, value: value === 0 ? .1 : value }));

  return (
    // ResponsiveContainer ensures the chart fits into the table cell.
    <ResponsiveContainer width="100%" height={20}>
      <BarChart data={chartData} barCategoryGap={1} barGap={2} >
        <Bar dataKey="value" >
            {/* We map over the data to color each bar individually */}
            {chartData.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  // Use a more vibrant color for bars with a value > 0
                  fill={entry.value > 0 ? '#bbd955' : '#f7c5d0'} 
                />
            ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );
};

export function Bookmark({
  coin,
  isBookmarked,
}: {
  coin: Coin;
  isBookmarked: boolean;
}) {
  const {
    createBookmark,
    removeBookmark,
    removeBookmarkIsPending,
    createBookmarkIsPending,
  } = useBookmarks({});
  const isPending = removeBookmarkIsPending || createBookmarkIsPending;
  const isLoggedIn = useUserLoggedIn();
  const router = useRouter();
  const pathname = usePathname();

  return (
    <div className="w-10">
      {isPending ? (
        <Loader />
      ) : (
        <picture>
          <img
            height={24}
            width={24}
            src={isBookmarked ? "/star-filled.svg" : "/star.svg"}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();

              if (!isLoggedIn) {
                router.push(
                  `/login?from=${pathname === "/" ? "/#assetTable" : pathname}`
                );
                return;
              }
              if (isBookmarked) {
                removeBookmark(coin.uuid);
              } else {
                createBookmark(coin.uuid);
              }
            }}
            alt={coin.name}
          />
        </picture>
      )}
    </div>
  );
}


// Main Table Component
export function AssetTable() {
  const router = useRouter();
  const [order, setOrder] = useState("DESC");
  const [orderBy, setOrderBy] = useState("marketCap");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  const [search, setSearch] = useState("");
  const [activeTab, setActiveTab] = useState<"crypto" | "stable" | "cbdc">("stable");
  const query = useDebounce(search, 500);
  const { width } = useScreenSize();
  const isMobile = width! < 768;

  const isStableCoinsEnabled = activeTab === 'stable';

  // Columns for Crypto and Stable Coins (no changes here)
  const cryptoColumns: TableProps<Coin>["columns"] = [
    {
      title: "",
      key: "actions01",
      dataIndex: "isBookmarked",
      width: 45,
      render: (isBookmarked: boolean, coin: Coin) => (
        <Bookmark coin={coin} isBookmarked={isBookmarked} />
      ),
    },
    {
      title: "#",
      key: "index",
      fixed: "left",
      dataIndex: "index",
      width: 50,
      render: (text: string) => (
        <span className="text-sm text-text-primary">{text}</span>
      ),
    },
    {
      title: "Asset",
      dataIndex: "name",
      key: "name",
      fixed: "left",
      align: "left",
      width: isMobile ? 100 : 200,
      render: (text: string, coin) => (
        <Link href={`/coins/${convertToKebabCase(text)}`}>
          <div className="flex gap-2">
            <picture>
              <img height={24} width={24} src={coin.iconUrl} alt={coin.name} />
            </picture>
            <span className="hidden md:block text-base font-medium text-text-primary">
              {shortenText(text, 15)}{" "}
              <span className="text-sm text-text-primary">({coin.symbol})</span>
            </span>
          </div>
        </Link>
      ),
    },
    {
      title: "Prices",
      dataIndex: "price",
      key: "price",
      width: 120,
      align: "left",
      sorter: true,
      render: (text: string) => (
        <span className="text-sm text-text-primary">
          ${isStableCoinsEnabled? text: formatPrice(parseFloat(text))}
        </span>
      ),
    },
    ...(
      !isStableCoinsEnabled ?
      [
        {
        title: "1h",
        dataIndex: "change_1h",
        key: "change_1h",
        sorter: true,
        align: "left" as const, 
        width: 100,
        render: (text: string) => <AssetValueChange value={text} />,
      },
    {
      title: "24h",
      dataIndex: "change_24h",
      key: "change_24h",
      sorter: true,
      align: "left" as const,
      width: 100,
      render: (text: string) => <AssetValueChange value={text} />,
    },
    {
      title: "7d",
      dataIndex: "change_7d",
      key: "change_7d",
      sorter: true,
      align: "left" as const,
      width: 100,
      render: (text: string) => <AssetValueChange value={text} />,
    }] :
      [
    {
      title: (
        <div className="flex flex-col items-center">
          <span>24h</span>
          <span>(Change)</span>
        </div>
      ),
      dataIndex: "change_24h",
      key: "change_24h",
      sorter: true,
      align: "left" as const,
      width: 100,
      render: (text: string) => <AssetValueChange value={text} />,
    },
    {
      title: (
        <div className="flex flex-col items-center">
          <span>Stable Coin</span>
          <span>Dominance</span>
          <span>(Volume)</span>
        </div>
      ),
      dataIndex: "volume24hPercentage",
      key: "volume24hPercentage",
      sorter: true,
      align: "center" as const,
      width: 120,
      render: (text: string) => <span>{text}%</span>,
    },
    {
      title: (
        <div className="flex flex-col items-center">
          <span>Stable Coin</span>
          <span>Dominance</span>
          <span>(Market Cap)</span>
        </div>
      ),
      dataIndex: "marketCapPercentage",
      key: "marketCapPercentage",
      sorter: true,
      align: "center" as const,
      width: 140,
      render: (text: string) => <span>{text}%</span>,
    }
      ]
    ),
    
    {
      title: "24h Volume",
      dataIndex: "24hVolume",
      key: "24hVolume",
      sorter: true,
      align: "left",
      width: 150,
      render: (text: string) => (
        <span className="text-sm text-text-primary">
          ${parseFloat(text).toLocaleString()}
        </span>
      ),
    },
    {
      title: "Market Cap",
      dataIndex: "marketCap",
      key: "marketCap",
      sorter: true,
      align: "left",
      width: 150,
      render: (text: string) => (
        <span className="text-sm text-text-primary">
          ${parseFloat(text).toLocaleString()}
        </span>
      ),
    },
    {
      title: (
        <div className="flex flex-col items-center">
          <span>{isStableCoinsEnabled ? "Digital Asset Dominance": "Dominance"}</span>
          <span>(Market Cap)</span>
        </div>
      ),
      dataIndex: "dominance",
      key: "dominance",
      width: 150,
      align: "center",
      render: (text: string) => (
        <span className="text-sm text-text-primary text-center">{text}%</span>
      ),
    },
  ];

  // Columns for the CBDC table
  const cbdcColumns: TableProps<CBDCCurrency>['columns'] = [
      {
          title: 'Digital currency',
          dataIndex: 'digitalCurrency',
          key: 'digitalCurrency',
          width: 200,
          align: "left",
          render: (text) => <span className="font-medium text-text-primary">{text}</span>,
      },
      {
          title: 'Country / Region',
          dataIndex: 'country',
          key: 'country',
          align: "left",
          width: 150,
      },
      {
          title: 'Central Bank(s)',
          dataIndex: 'centralBank',
          key: 'centralBank',
          align: "left",
          width: 250,
      },
      {
          title: 'Announcement Year',
          dataIndex: 'announcementYear',
          key: 'announcementYear',
          align: 'center',
          width: 150,
          sorter: (a, b) => a.announcementYear - b.announcementYear,
      },
      {
          title: 'Status',
          dataIndex: 'status',
          key: 'status',
          width: 150,
          align: "left",
          filters: [
                { text: 'Pilot', value: 'Pilot' },
                { text: 'Proof of concept', value: 'Proof of concept' },
                { text: 'Research', value: 'Research' },
                { text: 'Launched', value: 'Launched' },
                { text: 'Cancelled', value: 'Cancelled' },
          ],
          onFilter: (value, record) => record.status.indexOf(value as string) === 0,
          render: (status: string) => {
              // Define a variable ONLY for the box's background color
              let boxColor = 'bg-gray-300'; // A neutral default

              // Set colors based on the image provided
              if (status === 'Pilot') {
                  boxColor = 'bg-orange-200'; // Peach/light orange
              }
              if (status === 'Proof of concept') {
                  boxColor = 'bg-purple-300'; // Light purple
              }
              if (status === 'Research') {
                  boxColor = 'bg-teal-200'; // Light teal/green
              }
              if (status === 'Launched') {
                  boxColor = 'bg-green-300';
              }
              if (status === 'Cancelled') {
                  boxColor = 'bg-red-300';
              }
              
              return (
                <div className="flex items-center gap-2">
                    {/* The box is a square (no rounded class) */}
                    <div className={`w-3 h-3 rounded ${boxColor}`}></div>
                    {/* The text has a standard color, consistent with your app */}
                    <span className="font-semibold text-text-primary">{status}</span>
                </div>
              );
          },
      },
      {
        title: 'Update Rate',
        dataIndex: 'updateRate',
        key: 'updateRate',
        width: 150,
        align: 'center',
        render: (rates: number[]) => <UpdateRateChart rates={rates} />,
      }
  ];
  
  // Fetch crypto data
  const coinsQuery = useCoins({
    orderBy,
    offset: page === 1 ? 0 : +page * 10,
    limit: +limit,
    order,
    searchText: query,
    isStableCoinsEnabled: activeTab === 'stable',
  });
  
  // Fetch CBDC data only when the tab is active
  const cbdcQuery = useCbdc();

  const coins = coinsQuery.data?.data.coins;
  const { userBookmarksData } = useBookmarks({});
  const bookmarkedCoins = userBookmarksData?.coins[0]?.map((coin) => coin.uuid);
  const coinList = coins?.map((coin, index) => ({
    ...coin,
    index: (+page - 1) * 20 + (index + 1),
    isBookmarked: bookmarkedCoins?.includes(coin.uuid),
    price: isStableCoinsEnabled ? String(Math.round(Number(coin.price) || 0)): coin.price
  }));
  const total = coinsQuery.data?.data.stats.total || 10;
  // Process the fetched CBDC data
  const processedCbdcData = cbdcQuery.data
    ? cbdcQuery.data
      .filter(item => item.digitalCurrency && item.country && item.centralBank && item.status)
      .map(item => ({
        ...item,
        announcementYear: new Date(item.announcementYear).getFullYear()
      }))
    : [];

  const handlePagination = (page: number) => {
    setPage(page);
    router.push(`/#assetTable`);
  };

  const handleSearch = (value: string) => {
    setSearch(value);
  };

  const handlePageSizeChange = (value: string) => {
    setLimit(+value);
    router.push(`/#assetTable`);
  };

  return (
    <div>
      <div className="mb-4 flex items-center md:flex-row flex-col gap-4">
        <div className="hidden md:flex flex-1" />
        <div className="flex gap-2 w-full justify-center md:w-auto">
          <Button
            variant={activeTab === 'crypto' ? 'default' : 'outline'}
            onClick={() => setActiveTab('crypto')}
            className="border-2 border-bg-primary p-5"
          >
            Cryptocurrencies
          </Button>
          <Button
            variant={activeTab === 'stable' ? 'default' : 'outline'}
            onClick={() => setActiveTab('stable')}
            className="border-2 border-bg-primary p-5"
          >
            Stable Coins
          </Button>
          <Button
            variant={activeTab === 'cbdc' ? 'default' : 'outline'}
            onClick={() => setActiveTab('cbdc')}
            className="border-2 border-bg-primary p-5"
          >
            CBDC
          </Button>
        </div>
        <div className="w-full flex-1 flex justify-center md:justify-end">
          {activeTab !== 'cbdc' && (
            <SearchBar
              handleSearch={handleSearch}
              placeholder=""
            />
          )}
        </div>
      </div>

      <ConfigProvider
        theme={{
          components: {
            Spin: {
              colorPrimary: "#bbd955",
            },
            Table: {
              fontFamily: "DM Sans",
              colorPrimary: "#bbd955",
            },
          },
        }}
      >
        {activeTab === 'cbdc' ? (
          <Table
            columns={cbdcColumns}
            dataSource={processedCbdcData}
            rowKey="uid"
            loading={cbdcQuery.isLoading}
            scroll={{ x: "600px" }}
            pagination={{ pageSize: 15 }}
            className="crypto-table"
          />
        ) : (
          <Table
            columns={cryptoColumns}
            dataSource={coinList || []}
            pagination={false}
            onChange={(_p, _f, s) => {
              // @ts-expect-error ts(2339)
              const orderBy = s.columnKey;
              // @ts-expect-error ts(2339)
              const order = s.order === "ascend" ? "ASC" : "DESC";
              setOrder(order);
              setOrderBy(orderBy!);
            }}
            scroll={{ x: "600px" }}
            loading={coinsQuery.isLoading}
            onRow={(record) => ({
              onClick: () => {
                router.push(`/coins/${convertToKebabCase(record.name)}`);
              },
            })}
            className="crypto-table"
          />
        )}
      </ConfigProvider>
      
      {activeTab !== 'cbdc' && (
        <div className="mt-4">
            <Pagination
            totalPages={total}
            currentPage={page}
            onPageChange={handlePagination}
            pageSizeOptions={[
                { label: "20", value: "20" },
                { label: "50", value: "50" },
                { label: "100", value: "100" },
            ]}
            onPageSizeChange={handlePageSizeChange}
            />
        </div>
      )}
    </div>
  );
}