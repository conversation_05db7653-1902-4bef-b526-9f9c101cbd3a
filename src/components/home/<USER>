"use client";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LabelList,
  Y<PERSON><PERSON>s,
  Cell,
} from "recharts";
import { formatLargeCurrency } from "../../lib/utils";
import { Coin } from "../../lib/api.interface";
import { useScreenSize } from "@/lib/hook";

const COLORS = ["	#f7931a", "#627eea", "	#26A17B", "	#2775CA", "#f3ba2f"];

function getBarColor(entry: Coin, index: number) {
  return COLORS[index % COLORS.length];
}

export default function BTCVsETHChart({ data = [] }: { data: Coin[] }) {
  const { width } = useScreenSize();
  const isMobile = width! <= 768;
  const maxVolume = Math.max(...data?.map((d) => +d["24hVolume"]));
  const paddedMax = Math.ceil((maxVolume + 1e9) / 1e9) * 1e9;

  return (
    <div
      style={{
        width: "100%",
        height: "113px",
        padding: "0 8px",
      }}
    >
      <ResponsiveContainer width="100%" height="120%">
        <BarChart
          data={data}
          barSize={isMobile ? 12 : 20}
          barCategoryGap={isMobile ? 10 : 18}
          margin={{ top: 10, right: 0, left: 0, bottom: 0 }}
        >
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis
            dataKey="name"
            tick={{ fontSize: isMobile ? 10 : 11 }}
            axisLine={false}
            tickLine={false}
          />
          <YAxis
            domain={[0, paddedMax]} // Prevents overflow
            hide={true} // Hides the axis visually
          />
          <Tooltip
            contentStyle={{
              color: "#000",
            }}
            formatter={(value, name) => [
              formatLargeCurrency(value.toLocaleString()),
              name,
            ]}
            cursor={false}
          />
          <Bar dataKey="24hVolume" fill="#9c9cfc" radius={[4, 4, 0, 0]}>
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={getBarColor(entry, index)} />
            ))}
            <LabelList
              dataKey="24hVolume"
              position="top"
              formatter={(value: number) =>
                formatLargeCurrency(value.toLocaleString())
              }
              style={{ fill: "#000", fontSize: 9 }}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
