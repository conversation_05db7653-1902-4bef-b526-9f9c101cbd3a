"use client";

import React from "react";
import { ChevronRight, Triangle } from "lucide-react";
import BTCVsETHChart from "@/components/home/<USER>";
import { formatPriceInRoman, formatPriceInRomanFull } from "@/lib/utils";
import Link from "next/link";
import { useCoinMarketScenario, useCoins, useCoinStats } from "@/lib/state";
import Loader from "../comman/Loader";

const MarketCapitalization = () => {
  const coinsQuery = useCoins({
    orderBy: "marketCap",
    offset: 0,
    limit: 80,
    order: "DESC",
    searchText: "",
    isStableCoinsEnabled: false,
  });
  const coins = coinsQuery.data?.data.coins || [];
  // const bitcoin = coins[0];

  const marketScenarioQuery = useCoinMarketScenario();

  const marketScenario = marketScenarioQuery.data;
  const trendingCoins = marketScenario?.trendingAssets.slice(0, 3) || [];
  // const topGainers = marketScenario?.topGainers.slice(0, 3) || [];
  const filterCoins = ["BTC", "ETH", "USDT", "XRP", "USDC","FDUSD","BSC-USD"]
  const coinsData = coins.filter(el => filterCoins.includes(el.symbol)) || [];

  const coinStatsQuery = useCoinStats();

  const stats = coinStatsQuery.data?.data;
  const totalMarketCap = stats?.totalMarketCap;
  const total24hVolume = stats?.total24hVolume;

  if (coinsQuery.isLoading || marketScenarioQuery.isLoading) {
    return <Loader />;
  }

  return (
    <>
      <div>
        <p className="text-center mb-4">
          The global cryptocurrency market cap is{" "}
          <span className="text-bg-primary border rounded p-0.5">
            ${formatPriceInRomanFull(+totalMarketCap!)}
          </span>{" "}
          with a 24 Hour trading volume of{" "}
          <span className="text-bg-primary border rounded p-0.5">
            ${formatPriceInRomanFull(+total24hVolume!)}
          </span>
        </p>
        <p className="text-center ">
          Digital assets are any assets that exist in a digital form and come
          with ownership rights. We are primarily talking about
          cryptocurrencies including stablecoins and central bank digital currency (CBDC).
        </p>
      </div>
      {/* <div className="flex gap-6 text-text-primary mt-8 overflow-x-auto">
        <div className="bg-white p-4 rounded-lg w-1/3 min-w-[307px]">
          <p className="flex justify-between text-lg mb-4">
            <span className="font-semibold">Crypto Market Cap</span>{" "}
            <span className="font-bold">
              ${formatPriceInRoman(+totalMarketCap!)}
            </span>
          </p>
          <p className="font-medium text-sm text-text-secondary">
            The crypto market cap is the total value of all coins combined. It
            is calculated by summing up the market caps of all individual coins.
          </p>
        </div>
        <div className="bg-white p-4 rounded-lg w-1/3 min-w-[307px]">
          <p className="flex justify-between text-lg mb-4">
            <span className="font-semibold">Trading Volume</span>{" "}
            <span className="font-bold">
              ${formatPriceInRoman(+total24hVolume!)}
            </span>
          </p>
          <p className="font-medium text-sm text-text-secondary">
            Adding volume is the total value of all trades within the last 24
            hours.
          </p>
        </div>
        <div className="bg-white p-4 rounded-lg w-1/3 min-w-[307px]">
          <p className="flex justify-between text-lg mb-4">
            <span className="font-semibold">BTC Dominance</span>{" "}
            <span className="font-bold">{bitcoin.dominance}%</span>
          </p>
          <p className="font-medium text-sm text-text-secondary">
            BTC dominance is the Bitcoin market cap versus the crypto market
            cap.
          </p>
        </div>
      </div> */}
      <div className="flex gap-6 text-text-primary mt-8 overflow-x-auto">
        <div className="bg-white p-4 rounded-lg w-1/3 min-w-[307px]">
          <div className="flex justify-between text-lg mb-4">
            <span className="font-semibold">Trending Assets</span>{" "}
            <div className="flex items-center gap-1 text-text-secondary text-sm">
              <Link href={"/trending-assets"} className="text-sm font-medium">
                View more
              </Link>
              <ChevronRight size={16} className="mt-[1px]" />
            </div>
          </div>
          <div className="space-y-4">
            {trendingCoins.map((asset) => (
              <div
                key={asset.name}
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-3">
                  <picture>
                    <img
                      src={asset.iconUrl}
                      alt={asset.name}
                      className="w-6 h-6"
                    />
                  </picture>
                  <span className="text-sm font-medium text-black dark:text-white">
                    {asset.name}
                  </span>
                </div>
                <div className="flex gap-2 text-sm font-medium">
                  <span className="text-black dark:text-white">
                    ${formatPriceInRoman(+asset.price)}
                  </span>
                  <span
                    className={`w-16 flex items-center  justify-between ${
                      !asset.change?.includes("-")
                        ? "text-green-600"
                        : "text-red-500"
                    }`}
                  >
                    <Triangle
                      className={`w-3 h-3 ${
                        !asset.change?.includes("-") ? "rotate-0" : "rotate-180"
                      }`}
                      fill={!asset.change?.includes("-") ? "green" : "red"}
                    />
                    {asset?.change}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
        {/* <div className="bg-white p-4 rounded-lg w-1/3 min-w-[307px]">
          <p className="flex justify-between text-lg mb-4">
            <span className="font-semibold">Top Gainers</span>{" "}
            <span className="flex items-center gap-1 text-text-secondary text-sm">
              <Link href={"/top-gainers"} className="text-sm font-medium">
                View more
              </Link>
              <ChevronRight size={16} className="mt-[1px]" />
            </span>
          </p>
          <div className="space-y-4">
            {topGainers.map((asset) => (
              <div
                key={asset.name}
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-3">
                  <picture>
                    <img
                      src={asset.iconUrl}
                      alt={asset.name}
                      className="w-6 h-6"
                    />
                  </picture>
                  <span className="text-sm font-medium text-black dark:text-white">
                    {asset.name}
                  </span>
                </div>
                <div className="flex gap-2 text-sm font-medium">
                  <span className="text-black dark:text-white">
                    ${formatPriceInRoman(+asset.price)}
                  </span>
                  <span
                    className={`w-16 flex items-center  justify-between ${
                      !asset.change?.includes("-")
                        ? "text-green-600"
                        : "text-red-500"
                    }`}
                  >
                    <Triangle
                      className={`w-3 h-3 ${
                        !asset.change?.includes("-") ? "rotate-0" : "rotate-180"
                      }`}
                      fill={!asset.change?.includes("-") ? "green" : "red"}
                    />
                    {asset?.change}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div> */}
        <div className="bg-white p-4 rounded-lg w-1/3 min-w-[900px]">
          <p className="flex justify-between text-lg">
            <span className="font-semibold">24 Hour volume</span>{" "}
            {/* <span className="flex items-center gap-1 text-text-secondary text-sm">
              <Link href={"/mofse-advance"} className="text-sm font-medium">
                View more
              </Link>
              <ChevronRight size={16} className="mt-[1px]" />
            </span> */}
          </p>
          <BTCVsETHChart data={coinsData} />
        </div>
      </div>
    </>
  );
};

export default MarketCapitalization;
