import React from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface AssetPriceCardProps {
  color: string;
  name: string;
  price: number | string;
  updatedAt: string;
  className?: string;
}

const AssetPriceCard: React.FC<AssetPriceCardProps> = ({
  color,
  name,
  price,
  updatedAt,
  className = "",
}) => {
  return (
    <div
      className={cn(
        "relative p-4 rounded-lg border border-border-light bg-white shadow-sm hover:shadow-md transition-shadow overflow-hidden",
        className
      )}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <span
            className="w-4 h-4 rounded-full"
            style={{ backgroundColor: color }}
          ></span>
          <span className="text-base font-semibold">{name}</span>
        </div>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex flex-col">
          <p className="font-bold text-lg mb-1">{price}</p>
          <div className="flex items-center gap-1 whitespace-nowrap pt-0.5 mr-4 mb-2">
            <Image src="/time-ago.svg" alt="time-ago" width={16} height={16} />
            <span className="text-[#516075] text-xs">
              Updated {updatedAt} ago
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssetPriceCard;
