"use client";

import { useState } from "react";
import debounce from "lodash/debounce";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { LearnCategory } from "@/lib/api.interface";
import { cn, formatDateTimeInYearMonthDay } from "@/lib/utils";
import SearchBar from "../comman/SearchBar";
import { Badge } from "../ui/badge";
import Link from "next/link";
import { useLearnBlogs, useUserLoggedIn } from "@/lib/state";
import { Pagination } from "../comman/Pagination";
import Loader from "../comman/Loader";

const difficultyOptions = [
  { label: "All", value: "" },
  { label: "Beginner", value: "EASY" },
  { label: "Intermediate", value: "MEDIUM" },
  { label: "Advanced", value: "HARD" },
];

const SearchFilter = ({ categoryList }: { categoryList: LearnCategory[] }) => {
  const [difficulty, setDifficulty] = useState("");
  const [search, setSearch] = useState("");
  const [category, setCategory] = useState("");
  const [page, setPage] = useState(1);
  const isLoggedIn = useUserLoggedIn();

  const learnBlogsQuery = useLearnBlogs({
    categoryId: category === "All" ? "" : category,
    page: `${page}`,
    size: "16",
    title: search,
    level: difficulty,
  });

  const learnBlogs = learnBlogsQuery.data?.learns;

  const handleSearch = debounce((term: string) => {
    setSearch(term);
  }, 500);

  const handleCategoryChange = (category: string) => {
    setCategory(category);
  };

  const handleDifficultyChange = (difficulty: string) => {
    setDifficulty(difficulty);
  };

  return (
    <>
      <div className="flex gap-4 my-4 flex-col md:flex-row ">
        <SearchBar
          defaultValue={search}
          handleSearch={handleSearch}
          placeholder="Search Topics"
        />

        <div>
          <Select onValueChange={handleCategoryChange}>
            <SelectTrigger className="w-[180px] py-6">
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              {categoryList.map((category) => (
                <SelectItem key={category._id} value={category._id}>
                  {category.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4 mb-8">
        <span>Difficulty Levels :</span>

        <div className="flex flex-col md:flex-row gap-2">
          {difficultyOptions.map((opt) => (
            <button
              className={cn(
                "px-3 py-1 border border-border-light text-text-primary rounded-md text-xl cursor-pointer",
                difficulty === opt.value
                  ? "bg-bg-black border-white text-white"
                  : "bg-white"
              )}
              key={opt.label}
              onClick={() => handleDifficultyChange(opt.value)}
            >
              {opt.label}
            </button>
          ))}
        </div>
      </div>

      {learnBlogsQuery.isLoading ? (
        <Loader />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {learnBlogs?.length === 0 ? <p>Coming Soon</p> : null}
          {learnBlogs?.map((blog) => (
            <Link
              href={
                isLoggedIn
                  ? `/learn/${blog.slug}`
                  : `/login?from=/learn/${blog.slug}`
              }
              key={blog._id}
              className="bg-white shadow-md rounded-lg overflow-hidden hover:border hover:border-bg-primary"
            >
              <picture>
                <img
                  src={blog.thumbnailUrl}
                  alt={blog.title}
                  className="w-full h-48 object-cover"
                />
              </picture>
              <div className="p-4">
                <div className="flex items-center justify-between">
                  <Badge className="rounded-full">
                    {
                      categoryList.filter(
                        (category) => category._id === blog.category
                      )[0]?.title
                    }
                  </Badge>
                  <span className="text-sm">
                    {formatDateTimeInYearMonthDay(blog.updatedAt)}
                  </span>
                </div>
                <h2 className="text-lg font-semibold mb-2 mt-2 text-left">
                  {blog.title}
                </h2>
                <p className="text-gray-600 mb-4 text-justify text-sm font-light">
                  {blog.content.headersDescription}
                </p>
              </div>
            </Link>
          ))}
        </div>
      )}

      <Pagination
        currentPage={page}
        totalPages={
          Math.ceil((learnBlogsQuery.data?.totalDocuments || 1) / 16) || 1
        }
        onPageChange={setPage}
        className="mt-8"
      />
    </>
  );
};

export default SearchFilter;
