"use client";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "recharts";
// import { formatChartDate } from "@/lib/utils";
import Image from "next/image";
import { formatChartDateWithoutYear, formatTooltip } from "./utils";

// Default chart colors - Bitcoin specific
const DEFAULT_COLORS = {
  BORDER: "#409E13",
  DARK: "#62A442",
  FILL: "#CAF2B6",
  WHITE: "#FFFFFF",
};

// Function to get coin-specific colors
function getCoinColors(coin?: string) {
  switch (coin?.toUpperCase()) {
    case "BTC":
    case "BITCOIN":
      return {
        FILL: "#f7931a",
        BORDER: "#e6830f",
      };
    case "ETH":
    case "ETHEREUM":
      return {
        FILL: "#8c8c8c",
        BORDER: "#2A2A2B",
      };
    case "USDT":
      return {
        FILL: "#26A17B",
        BORDER: "#1e8a66",
      };
    case "USDC":
      return {
        FILL: "#2775CA",
        BORDER: "#1f5fa3",
      };
    default:
      return {
        FILL: DEFAULT_COLORS.FILL,
        BORDER: DEFAULT_COLORS.BORDER,
      };
  }
}

// Data point type for the chart
export type DataPoint = {
  date: string;
  count: number;
  timestamp: number;
};

type ChartPropTypes = {
  data: DataPoint[];
  coin?: string;
  chartType?: "bitcoin" | "other";
  showDate?: boolean;
  filter?: string;
};

const GenericBarGraph = ({
  data,
  coin,
  // showDate,
  filter
}: ChartPropTypes) => {
  const colors = getCoinColors(coin);

  return (
    <div style={{ width: "100%", height: 400 , position: "relative"}}>
      <Image
        src="/MOFSE.svg"
        width={140}
        height={40}
        alt="Watermark"
        style={{
          zIndex: '1',
          position: "absolute",
          top: "20px", 
          right: "110px",
          opacity: 0.6,
          pointerEvents: "none", 
          filter: "grayscale(100%)"
        }}
      />
      <ResponsiveContainer width="104%" height={400}>
        <BarChart
          data={data}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
          <XAxis
            dataKey="date"
            tick={{ fontSize: 10 }}
            tickFormatter={(value) => formatChartDateWithoutYear(value)}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            orientation="right"
            domain={[0, "dataMax"]}
            tickFormatter={(tick) => `${tick / 1000}k`}
            tick={{ fontSize: 10 }}
            tickLine={false}
            axisLine={false}
          />
         <Tooltip
            labelStyle={{ color: "#000000" }}
            formatter={(value) =>  formatTooltip(value as string, filter || "")}
          />
          <Bar
            dataKey="count"
            fill={colors.FILL}
            stroke={colors.BORDER}
            strokeWidth={1}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default GenericBarGraph;
