"use client";
import React, { useState, useMemo } from "react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { cn } from "@/lib/utils";
import {
  useBitcoinNumberOfTransactions,
  useEthereumNumberOfAddress,
  useEthereumNumberOfTransactions,
  useBitcoinNumberOfAddress,
  useUsdtNumberOfTransactions,
  useUsdcNumberOfTransactions,
} from "@/lib/state";
import TransactionCountHistoryChart, {
  DataPoint,
} from "./TransactionCountHistoryChart";
import { calculateDateRange } from "./utils";
import Loader from "../comman/Loader";
import { FILTER_LIST } from "./constant";
import TransactionCountHistoryBarGraph from "./TransactionCountHistoryBarGraph";
import Image from "next/image";
import { ListFilter } from "lucide-react";

// Time range options
const timeRangeOptions = ["24h", "7d", "1m"];

const TransactionCountChart = ({
  assetType,
  filter,
}: {
  assetType: string;
  filter: string;
}) => {

  const [selectedBlockchain, setSelectedBlockchain] = useState<string>("Ethereum");

  const isBitcoin = assetType === "BTC";
  const isEthereum = assetType === "ETH";
  const isUSDT = assetType === "USDT";
  const isUSDC = assetType === "USDC";

  const [timeRange, setTimeRange] = useState<string>("6m");
  const showDate = ["7d", "1m", "6m"].includes(timeRange);

  const { startDate, endDate } = useMemo(
    () => calculateDateRange(timeRange),
    [timeRange]
  );

  const bitcoinNumberOfTransactionsQuery = useBitcoinNumberOfTransactions(
    startDate,
    endDate
  );

  const ethereumNumberOfTransactionsQuery = useEthereumNumberOfTransactions(
    startDate,
    endDate
  );

  const ethereumNumberOfAddressQuery = useEthereumNumberOfAddress(
    startDate,
    endDate
  );

  const bitcoinNumberOfAddressQuery = useBitcoinNumberOfAddress(
    startDate,
    endDate
  );

  const usdtNumberOfTransactionsQuery = useUsdtNumberOfTransactions(
    startDate,
    endDate
  );

  const usdcNumberOfTransactionsQuery = useUsdcNumberOfTransactions(
    startDate,
    endDate
  );

  function getTransactionData() {
    if (isBitcoin) {
      if (filter === FILTER_LIST.TRANSACTION_COUNT) {
        return {
          data: bitcoinNumberOfAddressQuery.data?.map((item) => ({
            date: item.date,
            count: item.totalCount,
          })),
          isLoading: bitcoinNumberOfAddressQuery.isLoading,
        };
      }
      return {
        data: bitcoinNumberOfTransactionsQuery.data,
        isLoading: bitcoinNumberOfTransactionsQuery.isLoading,
      };
    }
    if (isEthereum) {
      if (filter === FILTER_LIST.SENDING_ADDRESS) {
        return {
          data: ethereumNumberOfAddressQuery.data?.map((item) => ({
            date: item.date,
            count: item.senderCount,
          })),
          isLoading: ethereumNumberOfAddressQuery.isLoading,
        };
      }
      if (filter === FILTER_LIST.ACTIVE_ADDRESS) {
        return {
          data: ethereumNumberOfAddressQuery.data?.map((item) => ({
            date: item.date,
            count: item.totalCount,
          })),
          isLoading: ethereumNumberOfAddressQuery.isLoading,
        };
      }

      if (filter === FILTER_LIST.RECEIVING_ADDRESS) {
        return {
          data: ethereumNumberOfAddressQuery.data?.map((item) => ({
            date: item.date,
            count: item.recieverCount,
          })),
          isLoading: ethereumNumberOfAddressQuery.isLoading,
        };
      }
      if (filter === FILTER_LIST.TRANSACTION_COUNT) {
        return {
          data: ethereumNumberOfTransactionsQuery.data?.map((item) => ({
            date: item.date,
            count: item.count,
          })),
          isLoading: ethereumNumberOfTransactionsQuery.isLoading,
        };
      }
      return {
        data: ethereumNumberOfTransactionsQuery.data,
        isLoading: ethereumNumberOfTransactionsQuery.isLoading,
      };
    }
    if (isUSDT) {
      if (filter === FILTER_LIST.ACTIVE_ADDRESS) {
        return {
          data: usdtNumberOfTransactionsQuery.data?.map((item) => ({
            date: item.date,
            count: item.count,
          })),
          isLoading: usdtNumberOfTransactionsQuery.isLoading,
        };
      }
      return {
        data: usdtNumberOfTransactionsQuery.data,
        isLoading: usdtNumberOfTransactionsQuery.isLoading,
      };
    }
    if (isUSDC) {
      if (filter === FILTER_LIST.ACTIVE_ADDRESS) {
        return {
          data: usdcNumberOfTransactionsQuery.data?.map((item) => ({
            date: item.date,
            count: item.count,
          })),
          isLoading: usdcNumberOfTransactionsQuery.isLoading,
        };
      }
      return {
        data: usdcNumberOfTransactionsQuery.data,
        isLoading: usdcNumberOfTransactionsQuery.isLoading,
      };
    }
    return { data: [], isLoading: false };
  }

  const { data, isLoading } = getTransactionData();

  const formattedData: DataPoint[] =
    data?.map((item) => ({
      date: item.date,
      count: item.count,
      timestamp: new Date(item.date).getTime(),
    })) || [];

  const chartData = formattedData.sort((a, b) => a.timestamp - b.timestamp);

  return (
    <div className="flex flex-col space-y-6">
      <div className="flex justify-end items-center">
        <div className="flex space-x-2">
          <ToggleGroup
            type="single"
            value={timeRange}
            onValueChange={(value) => value && setTimeRange(value)}
            className="flex rounded overflow-hidden  border border-border-light px-2 py-1 gap-2"
          >
            {timeRangeOptions.map((option) => (
              <ToggleGroupItem
                key={option}
                value={option}
                className={cn(
                  "outline-none border-none px-3 rounded last:rounded-tr last:rounded-br first:rounded-tl first:rounded-bl"
                )}
              >
                {option}
              </ToggleGroupItem>
            ))}
          </ToggleGroup>
        </div>
      </div>

      {/* Chart */}
      {isLoading ? (
        <Loader />
      ) : (
        <>
          {[
            FILTER_LIST.ACTIVE_ADDRESS,
            FILTER_LIST.RECEIVING_ADDRESS,
            FILTER_LIST.SENDING_ADDRESS,
          ].includes(filter) && (
            <TransactionCountHistoryChart
              data={chartData}
              showDate={showDate}
            />
          )}
          {filter === FILTER_LIST.TRANSACTION_COUNT && (
            <TransactionCountHistoryBarGraph
              data={chartData}
              coin={assetType}
              showDate={showDate}
            />
          )}
        </>
      )}
     {/* Blockchain Selection UI */}
      <div className="flex flex-col space-y-4">
        <div className="text-lg font-semibold text-white flex items-center gap-2"><ListFilter/> <span>Blockchains </span></div>
        <div className="flex flex-wrap gap-3">
          {BLOCKCHAINS.map((chain) => (
            <button
              key={chain.value}
              onClick={() => setSelectedBlockchain(chain.value)}
              className={cn(
                "flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors",
                "border border-gray-600 bg-transparent text-gray-300",
                "hover:bg-bg-primary hover:text-black hover:border-bg-primary",
                selectedBlockchain === chain.value
                  ? "bg-bg-primary text-black border-bg-primary"
                  : "",
                "focus:outline-none focus:ring-2 focus:ring-bg-primary focus:ring-offset-2 focus:ring-offset-gray-800"
              )}
            >
              <Image
                src={chain.icon}
                alt={chain.label}
                width={20}
                height={20}
                className="shrink-0"
              />
              <span>{chain.label}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TransactionCountChart;

const BLOCKCHAINS = [
  { value: "Aptos", label: "Aptos", icon: "/blockchains/aptos.svg" },
  { value: "Ethereum", label: "Ethereum", icon: "/blockchains/eth.svg" },
  { value: "Tron", label: "Tron", icon: "/blockchains/trx.svg" },
  { value: "Solana", label: "Solana", icon: "/blockchains/sol.svg" },
  { value: "BSC", label: "BSC", icon: "/blockchains/bnb.svg" }, 
  { value: "Avalanche", label: "Avalanche", icon: "/blockchains/avax.svg" },
  { value: "Arbitrum", label: "Arbitrum", icon: "/blockchains/arbitrum.svg" },
  { value: "Optimism", label: "Optimism", icon: "/blockchains/optimism.svg" },
  { value: "Polygon", label: "Polygon", icon: "/blockchains/matic.svg" }, 
  { value: "Base", label: "Base", icon: "/blockchains/base.svg" },
  { value: "Celo", label: "Celo", icon: "/blockchains/celo.svg" },
];
