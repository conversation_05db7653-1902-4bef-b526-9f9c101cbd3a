"use client";
import { formatChartDate } from "@/lib/utils";
import {
  <PERSON>,
  XA<PERSON>s,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  ComposedChart,
} from "recharts";

// Chart colors - Bitcoin specific
const COLORS = {
  BORDER: "#409E13",
  DARK: "#62A442",
  FILL: "#CAF2B6",
  WHITE: "#FFFFFF",
};

// Data point type for the chart
export type DataPoint = {
  date: string;
  count: number;
  timestamp: number;
};

type ChartPropTypes = {
  data: DataPoint[];
  chartType?: "bitcoin" | "other";
  showDate?: boolean;
};

const TransactionCountHistoryChart = ({ data, showDate }: ChartPropTypes) => {
  return (
    <div style={{ width: "100%", height: 400 }}>
      <ResponsiveContainer width="100%" height={400}>
        <ComposedChart data={data}>
          <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
          <XAxis
            dataKey="date"
            tick={{ fontSize: 10 }}
            tickFormatter={(value) => formatChartDate(value, { showDate })}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            orientation="right"
            domain={[0, "dataMax"]}
            tickFormatter={(tick) => `${tick / 1000}k`}
            tick={{ fontSize: 10 }}
            tickLine={false}
            axisLine={false}
          />
          <Tooltip
            formatter={(value) => [`${parseFloat(value as string)}`, "Count"]}
          />
          <Area
            type="monotone"
            dataKey="count"
            stroke="none"
            fill={COLORS.FILL}
            tooltipType="none"
          />
          <Line
            type="monotone"
            dataKey="count"
            stroke={COLORS.BORDER}
            strokeWidth={2}
            dot={false}
            activeDot={{ r: 6, stroke: COLORS.WHITE, strokeWidth: 2 }}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

export default TransactionCountHistoryChart;
