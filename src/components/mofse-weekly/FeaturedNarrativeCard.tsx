"use client";
import { Project } from "@/lib/api.interface";
import { useUserLoggedIn } from "@/lib/state";
import { formatDateTimeInYearMonthDay } from "@/lib/utils";
import Link from "next/link";
import React from "react";

const FeaturedNarrativeCard = ({ project }: { project: Project }) => {
  const isLoggedIn = useUserLoggedIn();

  return (
    <Link
      href={
        isLoggedIn
          ? `/mofse-weekly/${project.slug}`
          : `/login?from=/mofse-weekly/${project.slug}`
      }
      key={project._id}
      className="shadow-md rounded-lg overflow-hidden min-w-64 p-4 hover:border hover:border-bg-primary"
    >
      <picture>
        <img
          src={project.projectIconUrl}
          alt={project.title}
          className="w-full h-48 rounded-lg  object-cover"
        />
      </picture>
      <div className="mt-2">
        <h2 className="text-lg font-semibold mb-2">{project.title}</h2>
        <p className="text-gray-600 mb-4 text-justify text-sm font-light">
          {project?.content?.headersDescription}
        </p>
        <p className="text-sm">
          {formatDateTimeInYearMonthDay(project.updatedAt)}
        </p>
      </div>
    </Link>
  );
};

export default FeaturedNarrativeCard;
