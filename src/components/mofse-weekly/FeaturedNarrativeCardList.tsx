"use client";
import { Swiper, SwiperSlide } from "swiper/react";
// import { Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import Link from "next/link";
import { useProjects } from "@/lib/state";
import Loader from "../comman/Loader";

const FeaturedNarrativeCardList = () => {
  const projectsQuery = useProjects({
    title: "",
    page: 1,
    size: 16,
    order: "DESC",
  });
  const projectList = projectsQuery.data?.projects?.slice(0, 4) || [];

  if (projectsQuery.isLoading) {
    return (
      <div>
        <Loader />
      </div>
    );
  }

  return (
    <Swiper
      className="mt-12"
      // modules={[Autoplay]}
      spaceBetween={24}
      slidesPerView={4}
      // autoplay={{
      //   delay: 2500,
      //   disableOnInteraction: false,
      // }}
      loop={true}
      breakpoints={{
        320: { slidesPerView: 1 },
        640: { slidesPerView: 2 },
        768: { slidesPerView: 3 },
        1024: { slidesPerView: 4 },
      }}
      style={{
        height: 250,
      }}
    >
      {projectList.map((project) => (
        <SwiperSlide key={project._id} className="aspect-square">
          <Link
            href={`/mofse-weekly/${project.slug}`}
            key={project._id}
            className="block h-full w-full"
          >
            <div className="bg-white p-3 rounded-2xl shadow-md hover:border hover:border-bg-primary h-full w-full flex items-center justify-center">
              <div className="relative w-full h-full rounded-lg overflow-hidden">
                <picture>
                  <img
                    src={project.projectIconUrl}
                    alt={project.title}
                    className="w-full h-full object-cover"
                  />
                </picture>
              </div>
            </div>
          </Link>
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

export default FeaturedNarrativeCardList;
