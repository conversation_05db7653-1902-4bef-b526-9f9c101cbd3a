"use client";

import { useState } from "react";
import debounce from "lodash/debounce";

import SearchBar from "../comman/SearchBar";
import { Pagination } from "../comman/Pagination";
import { useProjects } from "@/lib/state";
import FeaturedNarrativeCard from "./FeaturedNarrativeCard";
import Loader from "../comman/Loader";

const SearchFilter = () => {
  const [search, setSearch] = useState("");
  const [page, setPage] = useState(1);

  const projectListQuery = useProjects({
    title: search,
    page,
    size: 16,
    order: "DESC",
  });
  const projectList = projectListQuery.data?.projects;

  const handleSearch = debounce((term: string) => {
    setSearch(term);
  }, 500);

  return (
    <>
      <div className="flex gap-4 mb-4">
        <SearchBar
          defaultValue={search}
          handleSearch={handleSearch}
          placeholder="Search for Mofse Weekly"
        />
      </div>

      {projectListQuery.isLoading ? (
        <Loader />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {projectList?.length === 0 && (
            <p className="text-lg text-text-secondary text-justify">
              Coming Soon
            </p>
          )}
          {projectList?.map((project) => (
            <FeaturedNarrativeCard key={project._id} project={project} />
          ))}
        </div>
      )}

      <Pagination
        currentPage={page}
        totalPages={
          Math.ceil((projectListQuery.data?.totalDocuments || 1) / 16) || 1
        }
        onPageChange={setPage}
        className="mt-8"
      />
    </>
  );
};

export default SearchFilter;
