"use client";

import { useProfile, useUserMutation } from "@/lib/state";
import Button from "../comman/Button";
import { Avatar, AvatarFallback } from "../ui/avatar";
import { getInitials } from "@/lib/utils";
import { Field } from "../comman/Field";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { TextField } from "../comman/TextField";
import { useEffect, useState } from "react";
import { api } from "@/lib/api";
import { toast } from "sonner";

const profileFormSchema = z.object({
  email: z.string().email("Invalid email"),
  username: z.string().min(3, "Username must be at least 3 characters"),
});

type ProfileFormData = z.infer<typeof profileFormSchema>;

const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, "0")}`;
};

const ProfileForm = () => {
  const profileQuery = useProfile();
  const user = profileQuery.data;
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [cooldownTime, setCooldownTime] = useState(0);
  const { updateUser, isUpdateUserPending } = useUserMutation(
    user?._id as string
  );

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      email: user?.email ?? "",
      username: user?.username ?? "",
    },
  });

  useEffect(() => {
    if (profileQuery.data) {
      form.reset({
        email: profileQuery.data.email,
        username: profileQuery.data.username,
      });
    }
  }, [profileQuery.data, form]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (cooldownTime > 0) {
      timer = setInterval(() => {
        setCooldownTime((time) => time - 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [cooldownTime]);

  const isNameChanged = form.watch("username") !== user?.username;

  const handleSendEmail = async () => {
    setIsSendingEmail(true);
    try {
      await api.auth.forgotPassword(user?.email as string);
      toast.success("Password reset email sent successfully");
      setCooldownTime(120);
    } catch (error: unknown) {
      const errorMessage = (error as { message: string }).message;
      toast.error(`Failed to send password reset email: ${errorMessage}`);
    }
    setIsSendingEmail(false);
  };

  const onSubmit = (data: ProfileFormData) => {
    updateUser({ username: data.username });
  };

  return (
    <>
      <div>
        <div className="flex items-center gap-4">
          <Avatar className="w-18 h-18 ml-2 ">
            <AvatarFallback className="bg-bg-primary text-text-primary text-3xl">
              {getInitials(user?.username ?? "")}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <p>{user?.username}</p>
            <p>User</p>
          </div>
        </div>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex gap-4 mt-4 items-start md:items-end md:flex-row flex-col"
        >
          <Field
            label="Name"
            required
            error={form.formState.errors.username?.message}
            control={
              <TextField placeholder="Name" {...form.register("username")} />
            }
            className="w-full md:w-1/3"
          />
          <Field
            label="Email"
            disabled
            required
            error={form.formState.errors.email?.message}
            control={
              <TextField placeholder="Email" {...form.register("email")} />
            }
            className="w-full md:w-1/3"
          />

          <Button
            loading={isSendingEmail}
            disabled={isSendingEmail || cooldownTime > 0}
            onClick={handleSendEmail}
          >
            {cooldownTime > 0
              ? `Resend in ${formatTime(cooldownTime)}`
              : "Change Password"}
          </Button>
        </form>
      </div>

      <div className="flex gap-2 mt-4">
        <Button variant="outline">Cancel</Button>
        <Button
          disabled={!isNameChanged}
          onClick={form.handleSubmit(onSubmit)}
          loading={isUpdateUserPending}
        >
          Save
        </Button>
      </div>
    </>
  );
};

export default ProfileForm;
