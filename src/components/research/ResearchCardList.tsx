"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import { Learn, ResearchCategory } from "@/lib/api.interface";
import Link from "next/link";
import { Badge } from "../ui/badge";
import { formatDateTimeInYearMonth, shortenText } from "@/lib/utils";

const ResearchCardList = ({
  ResearchList,
  categoryList,
}: {
  ResearchList: Learn[];
  categoryList: ResearchCategory[];
}) => {
  return (
    <Swiper
      className="mt-12 "
      modules={[Autoplay]}
      spaceBetween={24}
      slidesPerView={4}
      autoplay={{
        delay: 2500,
        disableOnInteraction: false,
      }}
      loop={true}
      breakpoints={{
        320: { slidesPerView: 1 },
        640: { slidesPerView: 2 },
        768: { slidesPerView: 3 },
        1024: { slidesPerView: 4 },
      }}
      style={{
        height: 400,
        paddingBottom: "4px",
      }}
    >
      {ResearchList.map((research) => (
        <SwiperSlide
          key={research._id}
          className="bg-white text-text-primary p-3 shadow-md rounded-2xl hover:border hover:border-bg-primary"
        >
          <Link href={`/research/${research.slug}`} key={research._id}>
            <picture>
              <img
                src={research.thumbnailUrl}
                alt={research.thumbnailUrl}
                className="w-full h-48 rounded-lg object-cover"
              />
            </picture>
            <div className="mt-4">
              <div className="flex items-center justify-between">
                <Badge className="rounded-full">
                  {
                    categoryList.filter(
                      (category) => category._id === research.category
                    )[0]?.title
                  }
                </Badge>
                <span className="text-sm text-text-secondary">{formatDateTimeInYearMonth(research.updatedAt)}</span>
              </div>
              <h2 className="text-lg font-semibold mb-2 mt-2 text-left">
                {research.title}
              </h2>
              <p className="text-gray-600 mb-4 text-justify text-sm font-light">
                {shortenText(research.content.about, 100)}
              </p>
            </div>
          </Link>
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

export default ResearchCardList;
