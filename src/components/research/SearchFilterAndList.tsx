"use client";

import { useState } from "react";
import debounce from "lodash/debounce";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { ResearchCategory } from "@/lib/api.interface";
import SearchBar from "../comman/SearchBar";
import { useResearchs, useUserLoggedIn } from "@/lib/state";
import Link from "next/link";
import { Badge } from "../ui/badge";
import { Pagination } from "../comman/Pagination";
import Loader from "../comman/Loader";
import { formatDateTimeInYearMonthDay, shortenText } from "@/lib/utils";

const SearchFilter = ({
  categoryList,
}: {
  categoryList: ResearchCategory[];
}) => {
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState("");
  const [category, setCategory] = useState("");
  const isLoggedIn = useUserLoggedIn();

  const researchListQuery = useResearchs({
    categoryId: category === "All" ? "" : category,
    page: page.toString(),
    size: "16",
    title: search,
    level: "",
  });
  const researchList = researchListQuery.data?.researches || [];

  const handleSearch = debounce((term: string) => {
    setSearch(term);
  }, 500);

  const handleCategoryChange = (category: string) => {
    setCategory(category);
  };

  return (
    <>
      <div className="flex gap-4 mb-4 flex-col md:flex-row">
        <SearchBar
          defaultValue={search || ""}
          handleSearch={handleSearch}
          placeholder="Search Topics"
        />

        <div>
          <Select onValueChange={handleCategoryChange}>
            <SelectTrigger className="w-[180px] py-6">
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              {categoryList.map((category) => (
                <SelectItem key={category._id} value={category._id}>
                  {category.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {researchListQuery.isLoading ? (
        <Loader />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {researchList.length === 0 ? <p>Coming Soon</p> : null}
          {researchList.map((research) => (
            <Link
              href={
                isLoggedIn
                  ? `/research/${research.slug}`
                  : `/login?from=/research/${research.slug}`
              }
              key={research._id}
              className="bg-white shadow-md rounded-lg overflow-hidden hover:border hover:border-bg-primary"
            >
              <picture>
                <img
                  src={research.thumbnailUrl}
                  alt={research.thumbnailUrl}
                  className="w-full h-48 object-cover"
                />
              </picture>
              <div className="p-4">
                <div className="flex justify-between items-center">
                  <Badge className="rounded-full">
                    {
                      categoryList.filter(
                        (category) => category._id === research.category
                      )[0]?.title
                    }
                  </Badge>
                  <span className="text-sm">
                    {formatDateTimeInYearMonthDay(research.updatedAt, false)}
                  </span>
                </div>
                <h2 className="text-lg font-semibold mb-2 mt-2 text-left">
                  {research.title}
                </h2>
                <p className="text-gray-600 mb-4 text-justify text-sm font-light">
                  {shortenText(research.content.about, 100)}
                </p>
              </div>
            </Link>
          ))}
        </div>
      )}

      <Pagination
        currentPage={page}
        totalPages={
          Math.ceil((researchListQuery.data?.totalDocuments || 1) / 16) || 1
        }
        onPageChange={setPage}
        className="mt-8"
      />
    </>
  );
};

export default SearchFilter;
