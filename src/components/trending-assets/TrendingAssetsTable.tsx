"use client";
import React from "react";
import { Coin } from "@/lib/api.interface";
import { AssetValueChange, Bookmark } from "../home/<USER>";
import { useBookmarks, useCoinMarketScenario } from "@/lib/state";
import { TableProps } from "antd";
import { convertToKebabCase, formatPrice, shortenText } from "@/lib/utils";
import { useScreenSize } from "@/lib/hook";
import Link from "next/link";
import { ConfigProvider, Table } from "antd";
import { useRouter } from "next/navigation";

const TrendingAssetsTable = () => {
  const router = useRouter();
  const marketScenarioQuery = useCoinMarketScenario();
  const coins = marketScenarioQuery.data?.trendingAssets || [];
  const { userBookmarksData } = useBookmarks({});
  const bookmarkedCoins = userBookmarksData?.coins[0]?.map((coin) => coin.uuid);

  const { width } = useScreenSize();
  const isMobile = width! < 768;

  const columns: TableProps<Coin>["columns"] = [
    {
      title: "",
      key: "actions01",
      dataIndex: "isBookmarked",
      width: 45,
      render: (isBookmarked: boolean, coin: Coin) => (
        <Bookmark coin={coin} isBookmarked={isBookmarked} />
      ),
    },
    {
      title: "#",
      key: "index",
      fixed: "left",
      dataIndex: "index",
      width: 50,
      render: (text: string) => (
        <span className="text-sm text-text-primary">{text}</span>
      ),
    },
    {
      title: "Asset",
      dataIndex: "name",
      key: "name",
      fixed: "left",
      align: "left",
      width: isMobile ? 100 : 200,
      render: (text: string, coin) => (
        <Link href={`/coins/${convertToKebabCase(text)}`}>
          <div className="flex gap-2">
            <picture>
              <img height={24} width={24} src={coin.iconUrl} alt={coin.name} />
            </picture>
            <span className="hidden md:block text-base font-medium text-text-primary">
              {shortenText(text, 15)}{" "}
              <span className="text-sm text-text-primary">({coin.symbol})</span>
            </span>
          </div>
        </Link>
      ),
    },
    {
      title: "Prices",
      dataIndex: "price",
      key: "price",
      width: 120,
      align: "left",
      sorter: (a, b) => parseFloat(a.price) - parseFloat(b.price),
      render: (text: string) => (
        <span className="text-sm text-text-primary">
          ${formatPrice(parseFloat(text))}
        </span>
      ),
    },
    {
      title: "1h",
      dataIndex: "change_1h",
      key: "change_1h",
      sorter: (a, b) => parseFloat(a.change_1h) - parseFloat(b.change_1h),
      align: "left",
      width: 100,
      render: (text: string) => <AssetValueChange value={text} />,
    },
    {
      title: "24h",
      dataIndex: "change",
      key: "change_24h",
      sorter: (a, b) => parseFloat(a.change) - parseFloat(b.change),
      align: "left",
      width: 100,
      render: (text: string) => <AssetValueChange value={text} />,
    },

    {
      title: "24h Volume",
      dataIndex: "24hVolume",
      key: "24hVolume",
      sorter: (a, b) => parseFloat(a["24hVolume"]) - parseFloat(b["24hVolume"]),
      align: "left",
      width: 150,
      render: (text: string) => (
        <span className="text-sm text-text-primary">
          ${parseFloat(text).toLocaleString()}
        </span>
      ),
    },
    {
      title: "Market Cap",
      dataIndex: "marketCap",
      key: "marketCap",
      sorter: (a, b) => parseFloat(a?.marketCap) - parseFloat(b?.marketCap),
      align: "left",
      width: 150,
      render: (text: string) => (
        <span className="text-sm text-text-primary">
          ${parseFloat(text).toLocaleString()}
        </span>
      ),
    },
  ];

  const coinsWithIndex = coins.map((coin, index) => ({
    ...coin,
    index: index + 1,
    isBookmarked: bookmarkedCoins?.includes(coin.uuid),
  }));

  return (
    <>
      <ConfigProvider
        theme={{
          components: {
            Spin: {
              colorPrimary: "#bbd955",
            },
            Table: {
              fontFamily: "DM Sans",
              colorPrimary: "#bbd955",
            },
          },
        }}
      >
        <Table
          columns={columns}
          dataSource={coinsWithIndex}
          pagination={false}
          loading={marketScenarioQuery.isLoading}
          className="crypto-table"
          onRow={(record) => ({
            onClick: () => {
              router.push(`/coins/${convertToKebabCase(record.name)}`);
            },
          })}
          scroll={{ x: "600px" }}
        />
      </ConfigProvider>
    </>
  );
};

export default TrendingAssetsTable;
