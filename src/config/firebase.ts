// Import the functions you need from the SDKs you need
import { initializeApp, getApps } from "firebase/app";
import { getAuth } from "firebase/auth";
import {
  getAnalytics,
  logEvent,
  setAnalyticsCollectionEnabled,
} from "firebase/analytics";
import { isProduction } from "../lib/constant";

// import { getAnalytics } from "firebase/analytics";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfigDev = {
  apiKey: "AIzaSyDj2lauAmN7EVUp9V08sp2hSHGwHGgFuSI",
  authDomain: "mofse-dev.firebaseapp.com",
  projectId: "mofse-dev",
  storageBucket: "mofse-dev.firebasestorage.app",
  messagingSenderId: "985916037568",
  appId: "1:985916037568:web:0ff48302bc73559f6a9025",
  measurementId: "G-0JVC88ZG69",
};

const firebaseConfigProd = {
  apiKey: "AIzaSyBMleLwy8aVFN0gZviy9Yr-q4qQNGMvuAQ",
  authDomain: "mofse-web.firebaseapp.com",
  projectId: "mofse-web",
  storageBucket: "mofse-web.firebasestorage.app",
  messagingSenderId: "819700592991",
  appId: "1:819700592991:web:35b67b34829724933a46ea",
  measurementId: "G-RKSSKMH225",
};

const firebaseConfig = isProduction ? firebaseConfigProd : firebaseConfigDev;

// Initialize Firebase
let app;
let analytics;

// Check if we're on the client side and if Firebase hasn't been initialized
if (typeof window !== "undefined" && !getApps().length) {
  app = initializeApp(firebaseConfig);
  // Initialize analytics only on client side
  analytics = getAnalytics(app);
  logEvent(analytics, "notification_received");
  setAnalyticsCollectionEnabled(analytics, false);
}

// Initialize Firebase for server side if not already initialized
if (!getApps().length) {
  app = initializeApp(firebaseConfig);
}

export const auth = typeof window !== "undefined" ? getAuth(app) : getAuth();
export const currentUser = auth;

export { app, analytics };

export default app;
