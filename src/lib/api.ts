import axios from "axios";
import { Time<PERSON>eriod } from "./types";
import {
  AffiliateLinksByCoidIDResponse,
  BitcoinAlertsApiResponse,
  BitcoinDominanceApiResponse,
  BitcoinNumberOfTransactionsApiResponse,
  BlockchainsApiResponse,
  BookmarkByIdResponse,
  BookmarksApiResponse,
  CategoriesApiResponse,
  CBDCData,
  CBDCTimelineData,
  CoinDetailsApiResponse,
  CoinMarketScenariosApiResponse,
  CoinPriceHistoryApiResponse,
  CoinsByCategoriesApiResponse,
  CoinStatsApiResponse,
  EmailVerificationResponse,
  EthereumNumberOfAddressApiResponse,
  ExchangeStatsApiResponse,
  FilterAlert,
  FinanceHistoryApiResponse,
  GetAllCoinsApiResponse,
  LearnBlogsApiResponse,
  LearnByCoidIDResponse,
  LearnByIdApiResponse,
  LearnCategoryByIdApiResponse,
  MofseAdvanceChartsApiResponse,
  OhlcDataApiResponse,
  PostApiResponse,
  ProfileApiResponse,
  ProjectByIdApiResponse,
  ProjectsApiResponse,
  RelatedProjectApiResponse,
  ResearchApiResponse,
  ResearchByIdApiResponse,
  ResearchCategoryApiResponse,
  ResetPasswordResponse,
} from "./api.interface";
import {
  getAuth,
  GoogleAuthProvider,
  onAuthStateChanged,
  signInWithPopup,
} from "firebase/auth";
import { baseUrl, STORAGE_KEYS } from "./constant";
import { auth } from "@/config/firebase";
import { format } from "date-fns";

let accessToken: string | null = null;
let lastFetchedTime = 0;

export async function getConfig() {
  onAuthStateChanged(auth, (user) => {
    if (user) {
      const fetchToken = async () => {
        accessToken = await user.getIdToken();
        lastFetchedTime = Date.now();
      };
      fetchToken().catch(console.error);
    } else {
      accessToken = null;
      lastFetchedTime = 0;
    }
  });

  const TOKEN_EXPIRY_TIME = 55 * 60 * 1000; // Refresh token 5 min before expiry

  if (!accessToken || Date.now() - lastFetchedTime > TOKEN_EXPIRY_TIME) {
    const user = auth.currentUser;
    if (user) {
      accessToken = await user.getIdToken(true); // Force refresh token
      lastFetchedTime = Date.now();
    }
  }

  return {
    headers: {
      Authorization: `Bearer ${accessToken || ""}`,
    },
  };
}

export const api = {
  firebase: {
    signInWithGoogle: async () => {
      const auth = getAuth();
      const provider = new GoogleAuthProvider();
      return signInWithPopup(auth, provider);
    },
  },
  auth: {
    isUserLoggedIn: async (): Promise<boolean> => {
      return new Promise((resolve) => {
        const auth = getAuth();

        // Check if the user is already available
        if (auth.currentUser) {
          resolve(true);
          return;
        }

        // Listen for auth state changes
        onAuthStateChanged(auth, (user) => {
          resolve(!!user);
          return;
        });

        return false;
      });
    },

    verifyEmail: async (email: string) => {
      const response = await axios.post<EmailVerificationResponse>(
        `${baseUrl}/users/auth/verification`,
        {
          email,
        }
      );

      if (response.status !== 200) {
        throw new Error("Error while verifying email");
      }

      return response.data.data;
    },

    resetPassword: async (email: string) => {
      const response = await axios.post<ResetPasswordResponse>(
        `${baseUrl}/users/auth/reset-password`,
        {
          email,
        }
      );

      if (response.status !== 200) {
        throw new Error("Error while resetting password");
      }

      return response.data.data;
    },
    forgotPassword: async (email: string) => {
      const response = await axios.post<ResetPasswordResponse>(
        `${baseUrl}/users/auth/forgot-password`,
        {
          email,
        }
      );

      if (response.status !== 200) {
        throw new Error("Error while resetting password");
      }

      return response.data.data;
    },

    updatePassword: async (password: string, token: string) => {
      const response = await axios.post<ResetPasswordResponse>(
        `${baseUrl}/users/auth/update-password`,
        {
          password,
          token,
        }
      );

      if (response.status !== 200) {
        throw new Error("Error while updating password");
      }

      return response.data.data;
    },
  },

  blockchains: {
    get: async () => {
      const response = await axios.get<BlockchainsApiResponse>(
        `${baseUrl}/coins/blockchain`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting blockchains");
      }

      return response?.data?.data;
    },
  },
  coins: {
    getAll: async (
      orderBy = "marketCap",
      offset = 0,
      limit = 20,
      order = "DESC",
      searchText = "",
      isStableCoinsEnabled = false
    ) => {
      let timePeriod = "7d";

      if (orderBy.includes("change")) {
        timePeriod = orderBy.split("_")[1];
        orderBy = "change";
      }

      const response = await axios.get<GetAllCoinsApiResponse>(
        `${baseUrl}/coins?offset=${offset}&limit=${limit}&orderBy=${orderBy}&searchText=${searchText}&sortOrder=${order}&timePeriod=${timePeriod}${
          isStableCoinsEnabled ? `&tags=stablecoin` : ""
        }`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting coins");
      }

      return response?.data?.data;
    },

    get: async (slug: string) => {
      const response = await axios.get<CoinDetailsApiResponse>(
        `${baseUrl}/coins/info/${slug}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting coin detail");
      }

      return response?.data?.data;
    },

    getPriceHistory: async (slug: string, timePeriod: TimePeriod) => {
      const response = await axios.get<CoinPriceHistoryApiResponse>(
        `${baseUrl}/coins/price-history/${slug}?timePeriod=${timePeriod}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting coin detail");
      }

      return response?.data?.data;
    },

    getMarketScenario: async () => {
      const response = await axios.get<CoinMarketScenariosApiResponse>(
        `${baseUrl}/coins/market-scenario`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting coin detail");
      }

      return response?.data?.data;
    },

    stats: async () => {
      const response = await axios.get<CoinStatsApiResponse>(
        `${baseUrl}/coins/stats`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting coin detail");
      }

      return response?.data?.data;
    },

    ohlc: async (coinId: string) => {
      const response = await axios.get<OhlcDataApiResponse>(
        `${baseUrl}/coins/ohlc/${coinId}?interval=week&limit=5000`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting coin detail");
      }

      return response?.data?.data;
    },

    financeHistory: async (frequency: string) => {
      const response = await axios.get<FinanceHistoryApiResponse>(
        `${baseUrl}/coins/finance-history?range=${frequency}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting coin detail");
      }

      return response?.data?.data;
    },
    exchanges: async (type: string) => {
      const response = await axios.get<ExchangeStatsApiResponse>(
        `${baseUrl}/coins/exchanges?type=${type}`,
        await getConfig()
      );
      if (response.status !== 200) {
        throw new Error("Error while getting coin detail");
      }
      return response?.data?.data;
    },
    cbdc: async () => {
       const response = await axios.get<CBDCData>(
        `${baseUrl}/coins/cbdc`,
        await getConfig()
      );
      if (response.status !== 200) {
        throw new Error("Error while getting coin detail");
      }
      return response?.data?.data;
    },
    cbdcTimeline: async (page: string, size: string, tag: string) => {
       const response = await axios.get<CBDCTimelineData>(
        `${baseUrl}/coins/cbdc-timeline?page=${page}&size=${size}&tag=${tag}`,
        await getConfig()
      );
      if (response.status !== 200) {
        throw new Error("Error while getting coin detail");
      }
      return response?.data?.data;
    }
  },

  s3: {
    getPresignedUrl: async (
      key: string,
      fileType: string,
      fileName: string
    ) => {
      const url = `${baseUrl}/preSigned`;
      const res = await axios.put<PostApiResponse>(
        url,
        { key, fileType, fileName },
        await getConfig()
      );

      const resData = res.data;

      if (res.status !== 200) {
        throw new Error(resData.data);
      }

      return resData;
    },
  },

  bookmarks: {
    getAll: async () => {
      const response = await axios.get<BookmarksApiResponse>(
        `${baseUrl}/bookmarks?size=1000`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting bookmarks");
      }

      return response.data.data;
    },
    getBookmarkById: async (
      bookmarkId: string,
      order = "DESC",
      orderBy = "marketCap"
    ) => {
      const response = await axios.get<BookmarkByIdResponse>(
        `${baseUrl}/bookmarks/${bookmarkId}?sortOrder=${order}&orderBy=${orderBy}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting bookmarks");
      }

      return response.data.data;
    },

    create: async (coinId: string, bookmarkId: string) => {
      const response = await axios.put<BookmarksApiResponse>(
        `${baseUrl}/bookmarks/${bookmarkId}`,
        { addcoinIds: [coinId] },
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while creating bookmark");
      }

      return response.data.data;
    },

    remove: async (coinId: string, bookmarkId: string) => {
      const response = await axios.put<BookmarksApiResponse>(
        `${baseUrl}/bookmarks/${bookmarkId}`,
        { removecoinIds: [coinId] },
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while removin bookmark");
      }

      return response.data.data;
    },
  },

  learns: {
    getByCoinId: async (coinId: string) => {
      const response = await axios.get<LearnByCoidIDResponse>(
        `${baseUrl}/learns/coinId/${coinId}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting Learn links");
      }

      return response.data.data;
    },
  },

  affiliates: {
    getByCoinId: async (coinId: string) => {
      const response = await axios.get<AffiliateLinksByCoidIDResponse>(
        `${baseUrl}/affliates/${coinId}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting Learn links");
      }

      return response.data.data;
    },
  },

  projects: {
    getAll: async (title = "", size: number, page = 1, order = "ASC") => {
      const startsAt =
        Math.round(new Date().getTime() / 1000) - 30 * 24 * 60 * 60;
      const endsAt = Math.round(new Date().getTime() / 1000);

      const response = await axios.get<ProjectsApiResponse>(
        `${baseUrl}/projects?title=${title}&startsAt=${startsAt}&endsAt=${endsAt}&size=${size}&page=${page}&order=${order}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting projects");
      }

      return response.data.data;
    },

    getById: async (id: string) => {
      const response = await axios.get<ProjectByIdApiResponse>(
        `${baseUrl}/projects/slug/${id}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting project by id");
      }

      return response.data.data;
    },

    getRelated: async (id: string) => {
      const response = await axios.get<RelatedProjectApiResponse>(
        `${baseUrl}/projects/related/${id}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting related projects");
      }

      return response.data.data;
    },
  },

  categories: {
    getAll: async (title: string) => {
      const response = await axios.get<CategoriesApiResponse>(
        `${baseUrl}/learn-categories?title=${title}`,
        await getConfig()
      );
      if (response.status !== 200) {
        throw new Error("Error while getting categories");
      }
      return response.data.data;
    },

    coinByCategories: async (categoryId: string) => {
      const response = await axios.get<CoinsByCategoriesApiResponse>(
        `${baseUrl}/categories/${categoryId}?offset=0&limit=100&orderBy=marketCap`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting categories");
      }
      return response.data.data;
    },

    getById: async (id: string) => {
      const response = await axios.get<LearnCategoryByIdApiResponse>(
        `${baseUrl}/learn-categories/${id}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting categories By Id");
      }

      return response.data.data;
    },
  },

  learnBlogs: {
    getAll: async (
      categoryId: string,
      size: string,
      page: string,
      title: string,
      level: string
    ) => {
      let response = await axios.get<LearnBlogsApiResponse>(
        `${baseUrl}/learns?size=${size}&page=${page}&title=${title}${
          level !== "" ? `&level=${level}` : ""
        }`
        // await getConfig()
      );

      if (categoryId) {
        response = await axios.get<LearnBlogsApiResponse>(
          `${baseUrl}/learns?categoryId=${categoryId}&size=${size}&page=${page}&title=${title}${
            level !== "" ? `&level=${level}` : ""
          }`
          // await getConfig()
        );
      }

      if (response.status !== 200) {
        throw new Error("Error while getting categories");
      }

      return response.data.data;
    },

    getAllAuthienticated: async () => {
      const response = await axios.get<LearnBlogsApiResponse>(
        `${baseUrl}/learns/authenticated`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting categories");
      }

      return response.data.data;
    },

    getById: async (learnId: string) => {
      const response = await axios.get<LearnByIdApiResponse>(
        `${baseUrl}/learns/slug/${learnId}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting Blog by Id");
      }

      return response.data.data;
    },
  },

  users: {
    create: async (body: unknown) => {
      const response = await axios.post<ProfileApiResponse>(
        `${baseUrl}/users`,
        body,
        await getConfig()
      );
      if (response.status !== 200) {
        throw new Error("Error while creating user");
      }
      return response.data.data;
    },
    profile: async () => {
      const response = await axios.get<ProfileApiResponse>(
        `${baseUrl}/users/profile`,
        await getConfig()
      );
      if (response.status !== 200) {
        throw new Error("Error while getting user profile");
      }
      return response.data.data;
    },

    update: async (id: string, body: unknown) => {
      const response = await axios.put<ProfileApiResponse>(
        `${baseUrl}/users/${id}`,
        body,
        await getConfig()
      );
      if (response.status !== 200) {
        throw new Error("Error while updating user profile");
      }
      return response.data.data;
    },

    onboarding: async (body: unknown) => {
      const response = await axios.post<ProfileApiResponse>(
        `${baseUrl}/users/onboarding`,
        body,
        await getConfig()
      );
      if (response.status !== 200) {
        throw new Error("Error while updating user profile");
      }
      return response.data.data;
    },
  },

  research: {
    getAll: async (
      categoryId: string,
      size: string,
      page: string,
      title: string,
      level: string
    ) => {
      let response = await axios.get<ResearchApiResponse>(
        `${baseUrl}/research?size=${size}&page=${page}&title=${title}${
          level !== "" ? `&level=${level}` : ""
        }`,
        await getConfig()
      );

      if (categoryId) {
        response = await axios.get<ResearchApiResponse>(
          `${baseUrl}/research?categoryId=${categoryId}&size=${size}&page=${page}&title=${title}${
            level !== "" ? `&level=${level}` : ""
          }`,
          await getConfig()
        );
      }

      if (response.status !== 200) {
        throw new Error("Error while getting research");
      }

      return response.data.data;
    },
    getById: async (id: string) => {
      const response = await axios.get<ResearchByIdApiResponse>(
        `${baseUrl}/research/slug/${id}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting research by Id");
      }

      return response.data.data;
    },

    getAllCategories: async () => {
      const response = await axios.get<ResearchCategoryApiResponse>(
        `${baseUrl}/research-categories`,
        await getConfig()
      );
      if (response.status !== 200) {
        throw new Error("Error while getting categories");
      }
      return response.data.data;
    },
  },

  marketCap: {
    bitcoinDominance: async () => {
      const response = await axios.get<BitcoinDominanceApiResponse>(
        `${baseUrl}/coins/marketCaps/Qwsogvtv82FCd`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting market cap dominance");
      }

      return response.data.data;
    },
  },

  bitcoinAlerts: {
    getAlertsHistory: async (
      page: number,
      from: string,
      amountInUsd: string,
      assetType: string
    ) => {
      const date = new Date();
      const formattedDate = format(date, "dd-MM-yyyy");

      const response = await axios.get<BitcoinAlertsApiResponse>(
        `${baseUrl}/blockchain/alerts-history?page=${page}&size=5&startsAt=${from}&endsAt=${formattedDate}${
          amountInUsd !== "" ? `&amountInUsd=${amountInUsd}` : ""
        }${assetType !== "" ? `&assetType=${assetType}` : ""}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting alerts history");
      }

      return response.data.data;
    },
  },
  alertFilter: {
    createAlertFilter: async (
      alertName: string,
      minimumTransferValue: string,
      selectedCoins: string[]
    ) => {
      const body = {
        alertName,
        minimumTransferValue,
        selectedCoins,
      };
      const alerts = localStorage.getItem(STORAGE_KEYS.ALERTS);
      if (alerts) {
        const parsedAlerts = JSON.parse(alerts);
        localStorage.setItem(
          STORAGE_KEYS.ALERTS,
          JSON.stringify([...parsedAlerts, body])
        );
      } else {
        localStorage.setItem(STORAGE_KEYS.ALERTS, JSON.stringify([body]));
      }
    },
    getAlerts: async (): Promise<FilterAlert[]> => {
      const alerts = localStorage.getItem(STORAGE_KEYS.ALERTS);
      if (alerts) {
        return JSON.parse(alerts);
      }
      return [];
    },
    deleteAlert: async (alertName: string) => {
      const alerts = localStorage.getItem(STORAGE_KEYS.ALERTS);
      if (alerts) {
        const parsedAlerts = JSON.parse(alerts);
        localStorage.setItem(
          STORAGE_KEYS.ALERTS,
          JSON.stringify(
            parsedAlerts.filter((alert: any) => alert.alertName !== alertName)
          )
        );
      }
    },
  },
  bitcoinNumberOfTransactions: {
    bitcoin: async (startsAt: string, endsAt: string) => {
      const response = await axios.get<BitcoinNumberOfTransactionsApiResponse>(
        `${baseUrl}/blockchain/bitcoin-number-of-transaction?startsAt=${startsAt}&endsAt=${endsAt}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting Bitcoin transaction count data");
      }

      return response.data.data;
    },
    ethereum: async (startsAt: string, endsAt: string) => {
      const response = await axios.get<BitcoinNumberOfTransactionsApiResponse>(
        `${baseUrl}/blockchain/no-of-transaction?startsAt=${startsAt}&endsAt=${endsAt}&assetType=ethereum`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting Ethereum transaction count data");
      }

      return response.data.data;
    },
    usdt: async (startsAt: string, endsAt: string) => {
      const response = await axios.get<BitcoinNumberOfTransactionsApiResponse>(
        `${baseUrl}/blockchain/no-of-transaction?startsAt=${startsAt}&endsAt=${endsAt}&assetType=usdt`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting USDT transaction count data");
      }

      return response.data.data;
    },
    usdc: async (startsAt: string, endsAt: string) => {
      const response = await axios.get<BitcoinNumberOfTransactionsApiResponse>(
        `${baseUrl}/blockchain/no-of-transaction?startsAt=${startsAt}&endsAt=${endsAt}&assetType=usdc`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting USDC transaction count data");
      }

      return response.data.data;
    },
    ethNumOfAddress: async (startsAt: string, endsAt: string) => {
      const response = await axios.get<EthereumNumberOfAddressApiResponse>(
        `${baseUrl}/blockchain/eth-no-of-address?startsAt=${startsAt}&endsAt=${endsAt}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting Ethereum number of address data");
      }

      return response.data.data;
    },
    bitcoinAddress: async (startsAt: string, endsAt: string) => {
      const response = await axios.get<EthereumNumberOfAddressApiResponse>(
        `${baseUrl}/blockchain/bitcoin-no-of-address?startsAt=${startsAt}&endsAt=${endsAt}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting Bitcoin number of address data");
      }

      return response.data.data;
    },
    usdThreshold: async (startsAt: string, endsAt: string, assetType: string, onChainFilter: string) => {
      const response = await axios.get<BitcoinNumberOfTransactionsApiResponse>(
        `${baseUrl}/blockchain/usd-threshold-transaction?startsAt=${startsAt}&endsAt=${endsAt}&assetType=${assetType}&onChainFilter=${onChainFilter}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting USD threshold transaction count data");
      }

      return response.data.data;
    },
  },
  mofseAdvanceCharts: {
     generic: async (assetType: string, filter: string, blockchain: string, startsAt: string, endsAt: string) => {
      const response = await axios.get<MofseAdvanceChartsApiResponse>(
        `${baseUrl}/mofse-advance-charts/generic?assetType=${assetType.toLowerCase()}&filter=${filter}&blockchain=${blockchain}&startsAt=${startsAt}&endsAt=${endsAt}`,
        await getConfig()
      );

      if (response.status !== 200) {
        throw new Error("Error while getting USDT transaction count data");
      }

      return response.data.data;
    },
  }
};
