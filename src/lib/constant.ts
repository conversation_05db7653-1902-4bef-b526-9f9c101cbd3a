export const isProduction =
  process.env.NEXT_PUBLIC_APP_ENVIROMENT === "production";
export const isDevelopment =
  process.env.NEXT_PUBLIC_APP_ENVIROMENT === "development";

export const APP_API_URL = process.env.NEXT_PUBLIC_APP_API_URL;

export const baseUrl = `${
  APP_API_URL ? APP_API_URL : "http://localhost:5001"
}/api/v1`;
export const PUBLIC_SITE_URL = process.env.NEXT_PUBLIC_SITE_URL;

export const STORAGE_KEYS = {
  LOGGED_IN_TIME: "LOGGED_IN_TIME",
  ALERTS: "ALERTS",
};

export const MIN_ALERT_AMOUNT = 50000000;
