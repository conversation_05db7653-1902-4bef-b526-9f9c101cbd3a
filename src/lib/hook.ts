"use client";
import { useEffect, useState } from "react";
import { BitcoinAlert } from "./api.interface";
import { baseUrl } from "./constant";
interface WindowSize {
  width: number | undefined;
  height: number | undefined;
}

export const useScreenSize = (): WindowSize => {
  const [windowSize, setWindowSize] = useState<WindowSize>({
    width: undefined,
    height: undefined,
  });

  useEffect(() => {
    const handleResize = (): void => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener("resize", handleResize);
    handleResize();

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return windowSize;
};

export function useDebounce(value: string, delay = 200) {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const timer = setTimeout(setDebouncedValue, delay, value);

    return () => clearTimeout(timer);
  }, [value, delay]);

  return debouncedValue;
}

export const useBTCTransactions = () => {
  const [transactions, setTransactions] = useState<BitcoinAlert | null>(null);

  useEffect(() => {
    const eventSource = new EventSource(`${baseUrl}/blockchain/stream/btc`);

    eventSource.addEventListener("alert-btc", (event) => {
      try {
        const newTransaction: BitcoinAlert = JSON.parse(event.data);
        setTransactions(newTransaction);
      } catch (err) {
        console.error("Failed to parse event data:", err);
      }
    });

    eventSource.onerror = (error) => {
      console.error("EventSource error:", error);
      eventSource.close();
    };

    // Clean up on component unmount
    return () => {
      eventSource.close();
    };
  }, []);

  return transactions;
};

export const useAllTransfers = () => {
  const btcTransfers = useBTCTransactions();

  return btcTransfers;
};

export const useEthTransactions = () => {
  const [transactions, setTransactions] = useState<BitcoinAlert | null>(null);

  useEffect(() => {
    const eventSource = new EventSource(`${baseUrl}/blockchain/stream/eth`);

    eventSource.addEventListener("alert-eth", (event) => {
      try {
        const newTransaction: BitcoinAlert = JSON.parse(event.data);
        setTransactions(newTransaction);
      } catch (err) {
        console.error("Failed to parse event data:", err);
      }
    });

    eventSource.onerror = (error) => {
      console.error("EventSource error:", error);
      eventSource.close();
    };

    // Clean up on component unmount
    return () => {
      eventSource.close();
    };
  }, []);

  return transactions;
};

export const useUSDTTransactions = () => {
  const [transactions, setTransactions] = useState<BitcoinAlert | null>(null);

  useEffect(() => {
    const eventSource = new EventSource(`${baseUrl}/blockchain/stream/usdt`);

    eventSource.addEventListener("alert-usdt", (event) => {
      try {
        const newTransaction: BitcoinAlert = JSON.parse(event.data);
        setTransactions(newTransaction);
      } catch (err) {
        console.error("Failed to parse event data:", err);
      }
    });

    eventSource.onerror = (error) => {
      console.error("EventSource error:", error);
      eventSource.close();
    };

    // Clean up on component unmount
    return () => {
      eventSource.close();
    };
  }, []);

  return transactions;
};

export const useUSDCTransactions = () => {
  const [transactions, setTransactions] = useState<BitcoinAlert | null>(null);

  useEffect(() => {
    const eventSource = new EventSource(`${baseUrl}/blockchain/stream/usdc`);

    eventSource.addEventListener("alert-usdc", (event) => {
      try {
        const newTransaction: BitcoinAlert = JSON.parse(event.data);
        setTransactions(newTransaction);
      } catch (err) {
        console.error("Failed to parse event data:", err);
      }
    });

    eventSource.onerror = (error) => {
      console.error("EventSource error:", error);
      eventSource.close();
    };

    // Clean up on component unmount
    return () => {
      eventSource.close();
    };
  }, []);

  return transactions;
};
