import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { api } from "./api";
import { TimePeriod } from "./types";
import { BitcoinAlert, BitcoinAlertsData, Bookmarks } from "./api.interface";
import { toast } from "sonner";
import { usePathname } from "next/navigation";
import {
  useBTCTransactions,
  useEthTransactions,
  useUSDCTransactions,
  useUSDTTransactions,
} from "./hook";
import { useEffect } from "react";
import { MIN_ALERT_AMOUNT } from "./constant";
// import axios from 'axios';

export const STATE_TYPES = {
  COINS: "coins",
  COIN: "coin",
  COIN_PRICE_HISTORY: "coinPriceHistory",
  BLOCKCHAINS: "blockchains",
  BOOKMARKS: "bookmarks",
  COIN_MARKET_SCENARIO: "coinMarketScenario",
  COIN_STATS: "coinStats",
  LEARNS: "learns",
  LEARN: "learn",
  AFFILIATES: "affiliates",
  USER_BOOKMARKS: "userBookmarks",
  PROJECTS: "projects",
  LEARN_CATEGORIES: "learnCategories",
  LEARN_BLOGS: "learnBlogs",
  LEARN_BLOGS_AUTH: "learnBlogsAuth",
  RELATED_PROJECTS: "relatedProjects",
  OHLC: "ohlc",
  CATEGORIES_COINS: "categoriesCoins",
  USER: "user",
  USER_LOGGED_IN: "userLoggedIn",
  RESEARCH: "research",
  RESEARCH_CATEGORIES: "researchCategories",
  BITCOIN_DOMINANCE: "bitcoinDominance",
  FINANCE_HISTORY: "financeHistory",
  EXCHANGES: "exchanges",
  BLOCKCHAIN_ALERTS: "blockchainAlerts",
  ALERTS: "alerts",
  BITCOIN_NUMBER_OF_TRANSACTIONS: "bitcoinNumberOfTransactions",
  ETHEREUM_NUMBER_OF_TRANSACTIONS: "ethereumNumberOfTransactions",
  ETHEREUM_NUMBER_OF_ADDRESS: "ethereumNumberOfAddress",
  BITCOIN_NUMBER_OF_ADDRESS: "bitcoinNumberOfAddress",
  USDT_NUMBER_OF_TRANSACTIONS: "usdtNumberOfTransactions",
  USDC_NUMBER_OF_TRANSACTIONS: "usdcNumberOfTransactions",
  USD_THRESHOLD_TRANSACTIONS: "usdThresholdTransactions",
};

export function useUserLoggedIn() {
  const profileQuery = useQuery({
    queryKey: [STATE_TYPES.USER_LOGGED_IN],
    queryFn: api.auth.isUserLoggedIn,
    refetchOnMount: true,
  });

  return profileQuery.data;
}

export function useCoin(slug: string) {
  const getCoinDetails = () => api.coins.get(slug);

  const coinQuery = useQuery({
    queryKey: [STATE_TYPES.COIN, slug],
    queryFn: getCoinDetails,
  });

  return coinQuery;
}

export function useCoinPriceHistory(slug: string, timePeriod: TimePeriod) {
  const getCoinPriceDetails = () => api.coins.getPriceHistory(slug, timePeriod);

  const coinPriceQuery = useQuery({
    queryKey: [STATE_TYPES.COIN_PRICE_HISTORY, timePeriod, slug],
    queryFn: getCoinPriceDetails,
    refetchInterval: 5 * 60 * 1000,
  });

  return coinPriceQuery;
}

export function useBlockchains() {
  const getBlockchains = () => api.blockchains.get();

  const blockchainsQuery = useQuery({
    queryKey: [STATE_TYPES.BLOCKCHAINS],
    queryFn: getBlockchains,
  });

  return blockchainsQuery;
}

export function useBookmarks({
  order,
  orderBy,
}: {
  order?: string;
  orderBy?: string;
}) {
  const queryClient = useQueryClient();
  const isUserLoggedIn = useUserLoggedIn();

  const getBookmarks = () => api.bookmarks.getAll();

  const bookmarksQuery = useQuery({
    queryKey: [STATE_TYPES.BOOKMARKS],
    queryFn: getBookmarks,
    enabled: isUserLoggedIn,
  });

  const [bookmark] = bookmarksQuery.data?.bookmarks || [];

  const createBookmark = (coinId: string) =>
    api.bookmarks.create(coinId, bookmark._id);

  const removeBookmark = (coinId: string) =>
    api.bookmarks.remove(coinId, bookmark._id);

  const getBookmarkById = () =>
    api.bookmarks.getBookmarkById(bookmark._id, order, orderBy);

  const mutation = useMutation({
    mutationFn: createBookmark,
    onSuccess: (_data, variableId) => {
      queryClient.setQueryData([STATE_TYPES.BOOKMARKS], (old: Bookmarks) => {
        if (!old?.bookmarks?.[0]) return old;

        return {
          ...old,
          bookmarks: [
            {
              ...old.bookmarks[0],
              coinIds: [...(old.bookmarks[0].coinIds || []), variableId],
            },
          ],
        };
      });

      queryClient.invalidateQueries({
        queryKey: [STATE_TYPES.USER_BOOKMARKS, order, orderBy],
      });
    },
  });

  const removeMutation = useMutation({
    mutationFn: removeBookmark,
    onSuccess: (_data, variableId) => {
      queryClient.setQueryData([STATE_TYPES.BOOKMARKS], (old: Bookmarks) => {
        if (!old?.bookmarks?.[0]) return old;

        return {
          ...old,
          bookmarks: [
            {
              ...old.bookmarks[0],
              coinIds: old.bookmarks[0].coinIds.filter(
                (id) => id !== variableId
              ),
            },
          ],
        };
      });

      queryClient.setQueryData(
        [STATE_TYPES.USER_BOOKMARKS, order, orderBy],
        (old: any) => {
          if (!old?.coins?.[0]) return old;

          return {
            ...old,
            title: old.title,
            coins: [
              // Filter out the removed coin from the coins array
              old.coins[0].filter(
                (coin: { uuid: string }) => coin.uuid !== variableId
              ),
              // Keep the stats object unchanged
              old.coins[1],
            ],
          };
        }
      );
    },
  });

  const userBookmarks = useQuery({
    queryKey: [STATE_TYPES.USER_BOOKMARKS, order, orderBy],
    queryFn: getBookmarkById,
    refetchOnMount: true,
  });

  return {
    bookmarks: bookmarksQuery.data,
    createBookmark: mutation.mutate,
    createBookmarkIsPending: mutation.isPending,
    removeBookmark: removeMutation.mutate,
    removeBookmarkIsPending: removeMutation.isPending,
    userBookmarksLoader: userBookmarks.isLoading,
    userBookmarksData: userBookmarks.data,
  };
}

export function useCoinMarketScenario() {
  const getCoinMarketScenario = () => api.coins.getMarketScenario();

  const coinMarketScenarioQuery = useQuery({
    queryKey: [STATE_TYPES.COIN_MARKET_SCENARIO],
    queryFn: getCoinMarketScenario,
  });

  return coinMarketScenarioQuery;
}

export function useCoins({
  orderBy,
  limit,
  offset,
  order,
  searchText = "",
  isStableCoinsEnabled = false,
  enabled = true,
}: {
  orderBy?: string;
  limit?: number;
  offset?: number;
  order?: string;
  searchText?: string;
  isStableCoinsEnabled?: boolean;
  enabled?: boolean;
}) {
  const getCoins = () =>
    api.coins.getAll(
      orderBy,
      offset,
      limit,
      order,
      searchText,
      isStableCoinsEnabled
    );

  const coinsQuery = useQuery({
    queryKey: [
      STATE_TYPES.COINS,
      orderBy,
      offset,
      limit,
      order,
      searchText,
      isStableCoinsEnabled,
    ],
    queryFn: getCoins,
    refetchOnMount: true,
    enabled,
  });

  return coinsQuery;
}

export function useCoinStats() {
  const getCoinStats = () => api.coins.stats();

  const coinStatsQuery = useQuery({
    queryKey: [STATE_TYPES.COIN_STATS],
    queryFn: getCoinStats,
  });

  return coinStatsQuery;
}

export function useLearns(id: string) {
  const getLearnByCoinId = () => api.learns.getByCoinId(id);

  const learnQuery = useQuery({
    queryKey: [STATE_TYPES.LEARNS, id],
    queryFn: getLearnByCoinId,
    enabled: !!id,
  });

  return learnQuery;
}

export function useAffiliate(id: string) {
  const getLearnByCoinId = () => api.affiliates.getByCoinId(id);

  const affiliateQuery = useQuery({
    queryKey: [STATE_TYPES.AFFILIATES, id],
    queryFn: getLearnByCoinId,
    enabled: !!id,
  });

  return affiliateQuery;
}

export function useProjects({
  title,
  size,
  page,
  order,
}: {
  title: string;
  page?: number;
  size: number;
  order?: "ASC" | "DESC";
}) {
  const getProjects = () => api.projects.getAll(title, size, page, order);

  const projectsQuery = useQuery({
    queryKey: [STATE_TYPES.PROJECTS, title, page, order],
    queryFn: getProjects,
  });

  return projectsQuery;
}

export function useLearnCategories({ title }: { title: string }) {
  const getLearnCategories = () => api.categories.getAll(title);

  const learnCategoriesQuery = useQuery({
    queryKey: [STATE_TYPES.LEARN_CATEGORIES, title],
    queryFn: getLearnCategories,
  });

  return learnCategoriesQuery;
}

export function useLearnBlogs({
  categoryId,
  page,
  size,
  title,
  level,
}: {
  categoryId: string;
  page: string;
  size: string;
  title?: string;
  level?: string;
}) {
  const isUserLoggedIn = useUserLoggedIn();
  const getLearns = () =>
    api.learnBlogs.getAll(categoryId, size, page, title!, level!);

  const getLearnAuthenticated = () => api.learnBlogs.getAllAuthienticated();

  const learnsQuery = useQuery({
    queryKey: [STATE_TYPES.LEARN_BLOGS, categoryId, title, level, page],
    queryFn: getLearns,
  });

  useQuery({
    queryKey: [STATE_TYPES.LEARN_BLOGS_AUTH],
    queryFn: getLearnAuthenticated,
    enabled: isUserLoggedIn,
  });

  return learnsQuery;
}

export function useLearnById({ id }: { id: string }) {
  const getLearnById = () => api.learnBlogs.getById(id);

  const learnQuery = useQuery({
    queryKey: [STATE_TYPES.LEARN, id],
    queryFn: getLearnById,
    enabled: !!id,
  });

  return learnQuery;
}

export function useLearnCategoriesById({ id }: { id: string }) {
  const getLearnCategoriesById = () => api.categories.getById(id);

  const learnCategoriesQuery = useQuery({
    queryKey: [STATE_TYPES.LEARN_CATEGORIES, id],
    queryFn: getLearnCategoriesById,
  });

  return learnCategoriesQuery;
}

export function useProjectById({ id }: { id: string }) {
  const getProjectById = () => api.projects.getById(id);

  const projectQuery = useQuery({
    queryKey: [STATE_TYPES.PROJECTS, id],
    queryFn: getProjectById,
    enabled: !!id,
  });

  return projectQuery;
}

export function useRelatedProjects({ id }: { id: string }) {
  const getRelatedProjects = () => api.projects.getRelated(id);

  const relatedProjectsQuery = useQuery({
    queryKey: [STATE_TYPES.RELATED_PROJECTS, id],
    queryFn: getRelatedProjects,
    enabled: !!id,
  });

  return relatedProjectsQuery;
}

export function useCoinOhlc({ id }: { id: string }) {
  const getCoinOhlc = () => api.coins.ohlc(id);

  const coinOhlcQuery = useQuery({
    queryKey: [STATE_TYPES.OHLC, id],
    queryFn: getCoinOhlc,
    enabled: !!id,
  });

  return coinOhlcQuery;
}

export function useCategoriesCoins({ id }: { id: string }) {
  const getCategoriesCoins = () => api.categories.coinByCategories(id);

  const categoriesCoinsQuery = useQuery({
    queryKey: [STATE_TYPES.CATEGORIES_COINS, id],
    queryFn: getCategoriesCoins,
    enabled: !!id,
  });

  return categoriesCoinsQuery;
}

export function useProfile() {
  const getProfile = () => api.users.profile();
  const isUserLoggedIn = useUserLoggedIn();

  const profileQuery = useQuery({
    queryKey: [STATE_TYPES.USER],
    queryFn: getProfile,
    enabled: isUserLoggedIn,
    retry: 2,
  });

  return profileQuery;
}

export function useUserMutation(id: string) {
  const queryClient = useQueryClient();
  const updateUser = (data: unknown) => api.users.update(id, data);

  const mutation = useMutation({
    mutationFn: updateUser,
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: [STATE_TYPES.USER] });
      toast.success("Profile updated successfully");
    },
  });

  return {
    updateUser: mutation.mutate,
    isUpdateUserPending: mutation.isPending,
  };
}

export function useResearchs({
  categoryId,
  page,
  size,
  title,
  level,
}: {
  categoryId: string;
  page: string;
  size: string;
  title?: string;
  level?: string;
}) {
  const getResearchs = () =>
    api.research.getAll(categoryId, size, page, title!, level!);

  const learnsQuery = useQuery({
    queryKey: [STATE_TYPES.RESEARCH, categoryId, title, level, page],
    queryFn: getResearchs,
  });

  return learnsQuery;
}

export function useResearchCategories() {
  const getResearchCategories = () => api.research.getAllCategories();

  const researchCategoriesQuery = useQuery({
    queryKey: [STATE_TYPES.RESEARCH_CATEGORIES],
    queryFn: getResearchCategories,
  });

  return researchCategoriesQuery;
}

export function useResearchById({ id }: { id: string }) {
  const getResearchById = () => api.research.getById(id);

  const researchQuery = useQuery({
    queryKey: [STATE_TYPES.RESEARCH, id],
    queryFn: getResearchById,
    enabled: !!id,
  });

  return researchQuery;
}

export function useBitcoinDominance() {
  const getBitcoinDominance = () => api.marketCap.bitcoinDominance();

  const bitcoinDominanceQuery = useQuery({
    queryKey: [STATE_TYPES.BITCOIN_DOMINANCE],
    queryFn: getBitcoinDominance,
  });

  return bitcoinDominanceQuery;
}

export function useFinanceHistory(frequency: string) {
  const getFinanceHistory = () => api.coins.financeHistory(frequency);

  const financeHistoryQuery = useQuery({
    queryKey: [STATE_TYPES.FINANCE_HISTORY, frequency],
    queryFn: getFinanceHistory,
    refetchInterval: 60 * 1000,
  });

  return financeHistoryQuery;
}

export function useExchanges(type: string) {
  const getExchanges = () => api.coins.exchanges(type);

  const exchangesQuery = useQuery({
    queryKey: [STATE_TYPES.EXCHANGES, type],
    queryFn: getExchanges,
  });

  return exchangesQuery;
}

export function useBlockchainHistory({
  page,
  selectedFilter,
  from,
  selectedCrypto,
}: {
  page: number;
  selectedFilter: string;
  from: string;
  selectedCrypto?: string;
}) {
  const queryClient = useQueryClient();

  const btcAlert = useBTCTransactions();
  const ethAlert = useEthTransactions();
  const usdtAlert = useUSDTTransactions();
  const usdcAlert = useUSDCTransactions();

  const alertFiltersQuery = useAlertFilters();
  const alertFiltersData = alertFiltersQuery.data;

  // Check if selectedFilter is a crypto filter (starts with "crypto:")
  const isCryptoFilter = selectedFilter.startsWith("crypto:");

  const findFilter = alertFiltersData?.find(
    (item) => item.alertName === selectedFilter
  );

  // If it's a crypto filter, use minimum alert amount, otherwise use filter's amount
  const amountInUsd = isCryptoFilter
    ? MIN_ALERT_AMOUNT.toString()
    : (findFilter ? findFilter.minimumTransferValue : MIN_ALERT_AMOUNT.toString());

  // Use selectedCrypto if provided, otherwise use filter's selected coins
  const assetType = selectedCrypto || (findFilter ? findFilter.selectedCoins.join(",") : "");
  const getBitcoinAlerts = () =>
    api.bitcoinAlerts.getAlertsHistory(page, from, amountInUsd, assetType);

  const bitcoinAlertsQuery = useQuery({
    queryKey: [
      STATE_TYPES.BLOCKCHAIN_ALERTS,
      page,
      from,
      amountInUsd,
      assetType,
      selectedCrypto,
    ],
    queryFn: getBitcoinAlerts,
    staleTime: 5 * 1000,
  });

  function addCoinAlertData(coinAlert: BitcoinAlert) {
    void queryClient.setQueryData(
      [STATE_TYPES.BLOCKCHAIN_ALERTS, 1, from, amountInUsd, assetType, selectedCrypto],
      (oldData: BitcoinAlertsData | undefined) => {
        if (!oldData)
          return {
            totalDocuments: 1,
            bitcoinAlerts: [coinAlert],
          };
        // Handle "All" filter (no crypto or custom filter selected)
        if (selectedFilter === MIN_ALERT_AMOUNT.toString() && !selectedCrypto) {
          const shouldAdd =
            parseFloat(coinAlert.amountInUsd) >= parseFloat(amountInUsd);

          if (!shouldAdd) return oldData;

          return {
            totalDocuments: oldData.totalDocuments + 1,
            bitcoinAlerts: [coinAlert, ...oldData.bitcoinAlerts]
              .slice(0, 5)
              .sort(
                (a, b) =>
                  new Date(b.createdAt).getTime() -
                  new Date(a.createdAt).getTime()
              ),
          };
        }

        // Handle crypto filter (Bitcoin, Ethereum, USDT, USDC)
        if (selectedCrypto) {
          const shouldAdd =
            parseFloat(coinAlert.amountInUsd) >= parseFloat(amountInUsd) &&
            coinAlert.assetType === selectedCrypto;

          if (!shouldAdd) return oldData;

          return {
            totalDocuments: oldData.totalDocuments + 1,
            bitcoinAlerts: [coinAlert, ...oldData.bitcoinAlerts]
              .slice(0, 5)
              .sort(
                (a, b) =>
                  new Date(b.createdAt).getTime() -
                  new Date(a.createdAt).getTime()
              ),
          };
        }

        if (!findFilter) return oldData;
        const symbol = coinAlert.assetType;

        const shouldAdd =
          parseFloat(coinAlert.amountInUsd) >= parseFloat(amountInUsd) &&
          findFilter?.selectedCoins.includes(symbol);

        if (!shouldAdd) return oldData;

        return {
          totalDocuments: oldData.totalDocuments + 1,
          bitcoinAlerts: [coinAlert, ...oldData.bitcoinAlerts]
            .slice(0, 5)
            .sort(
              (a, b) =>
                new Date(b.createdAt).getTime() -
                new Date(a.createdAt).getTime()
            ),
        };
      }
    );
  }

  useEffect(() => {
    if (btcAlert) {
      addCoinAlertData(btcAlert);
    }
    if (ethAlert) {
      addCoinAlertData(ethAlert);
    }
    if (usdtAlert) {
      addCoinAlertData(usdtAlert);
    }
    if (usdcAlert) {
      addCoinAlertData(usdcAlert);
    }
  }, [btcAlert, ethAlert, usdtAlert, usdcAlert]);

  return bitcoinAlertsQuery;
}

export function useAlertFilters() {
  const getAlerts = () => api.alertFilter.getAlerts();
  const path = usePathname();
  const enabled = path === "/alerts";

  const alertsQuery = useQuery({
    queryKey: [STATE_TYPES.ALERTS],
    queryFn: getAlerts,
    enabled,
  });

  return alertsQuery;
}

export function useCreateAlertsMutation() {
  const queryClient = useQueryClient();
  const createAlert = (data: {
    alertName: string;
    minimumTransferValue: string;
    selectedCoins: string[];
  }) =>
    api.alertFilter.createAlertFilter(
      data.alertName,
      data.minimumTransferValue,
      data.selectedCoins
    );

  const mutation = useMutation({
    mutationFn: createAlert,
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: [STATE_TYPES.ALERTS] });
      toast.success("Alert created successfully");
    },
  });

  return mutation;
}

export function useDeleteAlertMutation() {
  const queryClient = useQueryClient();
  const deleteAlert = (id: string) => api.alertFilter.deleteAlert(id);

  const mutation = useMutation({
    mutationFn: deleteAlert,
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: [STATE_TYPES.ALERTS] });
      toast.success("Alert deleted successfully");
    },
  });

  return mutation;
}

export function useBitcoinNumberOfTransactions(
  startDate: string,
  endDate: string
) {
  const getBitcoinNumberOfTransactions = () =>
    api.bitcoinNumberOfTransactions.bitcoin(startDate, endDate);

  const bitcoinNumberOfTransactionsQuery = useQuery({
    queryKey: [STATE_TYPES.BITCOIN_NUMBER_OF_TRANSACTIONS, startDate, endDate],
    queryFn: getBitcoinNumberOfTransactions,
  });

  return bitcoinNumberOfTransactionsQuery;
}

export function useUsdThresholdTransactions(
  startDate: string,
  endDate: string,
  assetType: string,
  onChainFilter: string
) {
  const getUsdThresholdTransactions = () =>
    api.bitcoinNumberOfTransactions.usdThreshold(startDate, endDate, assetType, onChainFilter);

  const usdThresholdTransactionsQuery = useQuery({
    queryKey: [STATE_TYPES.USD_THRESHOLD_TRANSACTIONS, startDate, endDate, assetType, onChainFilter],
    queryFn: getUsdThresholdTransactions,
  });

  return usdThresholdTransactionsQuery;
}

export function useEthereumNumberOfTransactions(
  startDate: string,
  endDate: string
) {
  const getEthereumNumberOfTransactions = () =>
    api.bitcoinNumberOfTransactions.ethereum(startDate, endDate);

  const ethereumNumberOfTransactionsQuery = useQuery({
    queryKey: [STATE_TYPES.ETHEREUM_NUMBER_OF_TRANSACTIONS, startDate, endDate],
    queryFn: getEthereumNumberOfTransactions,
  });

  return ethereumNumberOfTransactionsQuery;
}

export function useEthereumNumberOfAddress(startDate: string, endDate: string) {
  const getEthereumNumberOfAddress = () =>
    api.bitcoinNumberOfTransactions.ethNumOfAddress(startDate, endDate);

  const ethereumNumberOfAddressQuery = useQuery({
    queryKey: [STATE_TYPES.ETHEREUM_NUMBER_OF_ADDRESS, startDate, endDate],
    queryFn: getEthereumNumberOfAddress,
  });

  return ethereumNumberOfAddressQuery;
}

export function useBitcoinNumberOfAddress(startDate: string, endDate: string) {
  const getBitcoinNumberOfAddress = () =>
    api.bitcoinNumberOfTransactions.bitcoinAddress(startDate, endDate);

  const bitcoinNumberOfAddressQuery = useQuery({
    queryKey: [STATE_TYPES.BITCOIN_NUMBER_OF_ADDRESS, startDate, endDate],
    queryFn: getBitcoinNumberOfAddress,
  });

  return bitcoinNumberOfAddressQuery;
}

export function useUsdtNumberOfTransactions(
  startDate: string,
  endDate: string
) {
  const getUsdtNumberOfTransactions = () =>
    api.bitcoinNumberOfTransactions.usdt(startDate, endDate);

  const usdtNumberOfTransactionsQuery = useQuery({
    queryKey: [STATE_TYPES.USDT_NUMBER_OF_TRANSACTIONS, startDate, endDate],
    queryFn: getUsdtNumberOfTransactions,
  });

  return usdtNumberOfTransactionsQuery;
}

export function useUsdcNumberOfTransactions(
  startDate: string,
  endDate: string
) {
  const getUsdcNumberOfTransactions = () =>
    api.bitcoinNumberOfTransactions.usdc(startDate, endDate);

  const usdcNumberOfTransactionsQuery = useQuery({
    queryKey: [STATE_TYPES.USDC_NUMBER_OF_TRANSACTIONS, startDate, endDate],
    queryFn: getUsdcNumberOfTransactions,
  });

  return usdcNumberOfTransactionsQuery;
}


export function useGenericMofseAdvanceCharts(
  startDate: string,
  endDate: string,
  assetType: string,
  filter: string,
  blockchain: string
) {
  const getGenericMofseAdvanceCharts = () =>
    api.mofseAdvanceCharts.generic(assetType, filter, blockchain, startDate, endDate);

  const genericMofseAdvanceChartsQuery = useQuery({
    queryKey: [assetType, filter, blockchain, startDate, endDate],
    queryFn: getGenericMofseAdvanceCharts,
  });

  return genericMofseAdvanceChartsQuery;
}

// The custom hook to fetch CBDC data
export const useCbdc = () => {
  const getCbdcData = () =>
    api.coins.cbdc();

  return useQuery({
    queryKey: ['cbdc'],
    queryFn: getCbdcData
  });
};

export const useCbdcTimeline = (page: string, size: string, tag: string) => {
  const getCbdcTimelineData = () =>
    api.coins.cbdcTimeline(page, size, tag);
 
  return useQuery({
    queryKey: ['cbdc-timeline', page, size, tag],
    queryFn: getCbdcTimelineData,
    enabled: !!tag,
  });
};

